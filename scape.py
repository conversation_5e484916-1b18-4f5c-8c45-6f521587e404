#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频刮削模块
支持从多个数据源获取信息并生成NFO文件
- hanime1.me: 获取tag、中文名称、摘要、年份
- themoviedb: 获取中文摘要、评分
- getchu.com: 获取剧照图片、制作商信息
"""

import os
import re
import time
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urljoin, quote
import requests
from bs4 import BeautifulSoup
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException

class HanimeScraper:
    """Hanime1.me 增强版刮削器"""
    
    def __init__(self, config, logger):
        """初始化刮削器"""
        self.config = config
        self.logger = logger
        self.scraper = cloudscraper.create_scraper()
        
        # 从配置获取参数
        self.scraping_config = config.get('scraping', {})
        self.download_config = config.get('download', {})
        self.crawl_config = config.get('crawl', {})
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.crawl_config.get('settings', {}).get('user_agent', 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        }
        
        # 数据源配置
        self.sources = self.scraping_config.get('sources', {})
        
        # NFO配置
        self.nfo_config = self.scraping_config.get('nfo', {})
        
        # 图片下载目录
        self.image_dir = Path(self.download_config.get('download_dir', './downloads')) / 'images'
        self.image_dir.mkdir(parents=True, exist_ok=True)
    
    def extract_video_id_from_filename(self, filename):
        """从文件名提取视频ID"""
        # 优先查找特定模式的视频ID
        
        # 模式1: 纯数字ID，通常是6位数 (如: 102260-1080p.mp4 -> 102260)
        match = re.search(r'(\d{6,8})', filename)
        if match:
            return match.group(1)
        
        # 模式2: Video 后跟数字 (如: Video 101451_101451.mp4)
        match = re.search(r'Video\s*(\d+)', filename, re.IGNORECASE)
        if match:
            return match.group(1)
        
        # 模式3: 文件名开头的长数字ID
        match = re.search(r'^(\d{5,})', filename)
        if match:
            return match.group(1)
        
        # 模式4: 下划线或横线分隔的数字ID
        match = re.search(r'[-_](\d{5,})[-_]', filename)
        if match:
            return match.group(1)
        
        # 最后尝试: 任何5位以上的数字
        match = re.search(r'(\d{5,})', filename)
        if match:
            return match.group(1)
        
        self.logger.warning(f"无法从文件名提取有效的视频ID: {filename}")
        return None
    
    def setup_webdriver(self):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        
        # 最强SSL绕过设置
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        chrome_options.add_argument('--ignore-ssl-errors-accept-any-certs')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-javascript')
        chrome_options.add_argument('--accept-insecure-certs')
        chrome_options.add_argument('--allow-insecure-localhost')
        chrome_options.add_argument('--disable-tls13')
        chrome_options.add_argument('--ssl-version-fallback-min=tls1')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        
        # 禁用更多网络功能以减少SSL问题
        chrome_options.add_argument('--disable-background-networking')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--disable-translate')
        chrome_options.add_argument('--disable-ipc-flooding-protection')
        
        chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            # 设置更短的超时
            driver.set_page_load_timeout(15)
            driver.implicitly_wait(3)
            return driver
        except WebDriverException as e:
            self.logger.error(f"Chrome WebDriver 初始化失败: {e}")
            return None
    
    def scrape_hanime1(self, video_id):
        """从hanime1.me获取完整信息"""
        if not self.sources.get('hanime1', {}).get('enabled', True):
            return {}
        
        base_url = self.sources.get('hanime1', {}).get('base_url', 'https://hanime1.me')
        video_url = f"{base_url}/watch?v={video_id}"
        
        self.logger.info(f"从hanime1.me获取信息: {video_url}")
        
        driver = self.setup_webdriver()
        if not driver:
            return {}
        
        try:
            driver.get(video_url)
            driver.implicitly_wait(5)
            page_content = driver.page_source
            
            soup = BeautifulSoup(page_content, 'html.parser')
            
            info = {}
            
            # 1. 获取标题信息
            title_element = soup.find('h1')
            if title_element:
                title = title_element.get_text(strip=True)
                info['title'] = title
                info['originaltitle'] = title
                info['title_cn'] = title  # 中文标题
                info['title_jp'] = title  # 日文标题
                
                # 尝试提取罗马音标题
                title_rm_match = re.search(r'([A-Za-z\s\-]+)', title)
                if title_rm_match:
                    info['title_rm'] = title_rm_match.group(1).strip()
                else:
                    info['title_rm'] = title
            
            # 2. 获取页面标题（作为备用）
            page_title = soup.find('title')
            if page_title and not info.get('title'):
                title = page_title.get_text(strip=True)
                title = re.sub(r'\s*-\s*hanime1\.me.*$', '', title, flags=re.IGNORECASE)
                info['title'] = title
                info['title_cn'] = title
                info['title_jp'] = title
                info['title_rm'] = title
            
            # 3. 获取详细描述信息
            plot_candidates = []
            
            # 查找描述区域
            desc_selectors = [
                'div.description', 'div.plot', 'div.summary',
                'p.description', 'p.plot', 'p.summary',
                '.video-description', '.anime-description'
            ]
            
            for selector in desc_selectors:
                elements = soup.select(selector)
                plot_candidates.extend(elements)
            
            # 查找包含描述关键词的元素
            desc_keywords = ['あらすじ', '内容', 'ストーリー', '物语', '故事', '剧情']
            for keyword in desc_keywords:
                elements = soup.find_all(text=lambda x: x and keyword in x)
                for elem in elements:
                    parent = elem.find_parent()
                    if parent:
                        plot_candidates.append(parent)
            
            # 选择最好的描述
            best_plot = ""
            for candidate in plot_candidates:
                text = candidate.get_text(strip=True)
                if len(text) > len(best_plot) and len(text) > 50:
                    best_plot = text
            
            if best_plot:
                info['plot'] = f"<![CDATA[{best_plot}]]>"
                info['outline'] = best_plot[:200] + '...' if len(best_plot) > 200 else best_plot
            
            # 4. 获取年份和日期信息
            year_patterns = [
                r'(\d{4})年',
                r'(\d{4})-(\d{1,2})-(\d{1,2})',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
                r'(\d{4})'
            ]
            
            page_text = str(soup)
            for pattern in year_patterns:
                matches = re.findall(pattern, page_text)
                for match in matches:
                    if isinstance(match, tuple):
                        year = match[0]
                    else:
                        year = match
                    
                    if 1990 <= int(year) <= datetime.now().year + 1:
                        info['year'] = year
                        if len(match) == 3:  # 完整日期
                            info['premiered'] = f"{match[0]}-{match[1].zfill(2)}-{match[2].zfill(2)}"
                            info['releasedate'] = info['premiered']
                        else:
                            info['premiered'] = f"{year}-01-01"
                            info['releasedate'] = info['premiered']
                        break
                if 'year' in info:
                    break
            
            # 5. 获取标签信息（增强版）
            tags = set()
            genres = set()
            
            # 查找标签相关元素
            tag_selectors = [
                '.tag', '.tags', '.genre', '.genres', '.category', '.categories',
                'span[class*="tag"]', 'a[class*="tag"]', 'div[class*="tag"]',
                'span[class*="genre"]', 'a[class*="genre"]'
            ]
            
            for selector in tag_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    tag_text = elem.get_text(strip=True)
                    if tag_text and 1 < len(tag_text) < 20:
                        tags.add(tag_text)
                        genres.add(tag_text)
            
            # 查找特定的标签关键词
            tag_keywords = [
                '内射', '凌辱', '口交', '强制', '怪兽', '怀孕', '放尿', '捆绑', 
                '肛交', '触手', '贫乳', '阿嘿颜', '异物插入', '双洞齐下', '魔法少女',
                '中出し', '凌●', 'フェラ', '強制', 'モンスター', '妊娠', '放尿', '拘束',
                'アナル', '触手', '貧乳', 'アヘ顔', '異物挿入', '2穴', '魔法少女'
            ]
            
            for keyword in tag_keywords:
                if keyword in page_text:
                    tags.add(keyword)
                    genres.add(keyword)
            
            # 添加基础标签
            tags.add('里番')
            tags.add('魔法少女')
            genres.add('里番')
            genres.add('魔法少女')
            
            if tags:
                info['tag'] = list(tags)[:15]  # 限制标签数量
                info['genre'] = list(genres)[:15]
            
            # 6. 设置基础信息
            info['customrating'] = '里番'
            info['mpaa'] = '里番'
            info['rating'] = '7.5'  # 默认评分
            info['criticrating'] = '75'
            info['runtime'] = '30'  # 默认时长（分钟）
            info['hanime1'] = video_url
            info['uncensored'] = 'False'
            
            # 7. 尝试获取编号信息
            num_patterns = [
                r'([A-Z]{2,4}-\d{3,4})',  # GBR-033 格式
                r'([A-Z]+\d{3,4})',       # ABC123 格式
                r'(vol\.\d+)',            # vol.1 格式
            ]
            
            for pattern in num_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    info['num'] = match.group(1).upper()
                    break
            
            # 8. 查找相关网站链接
            links = soup.find_all('a', href=True)
            for link in links:
                href = link['href']
                if 'dmm.co.jp' in href:
                    info['website'] = href
                    break
            
            self.logger.debug(f"hanime1.me信息: {len(info)} 个字段")
            return info
            
        except Exception as e:
            self.logger.error(f"从hanime1.me获取信息失败: {e}")
            return {}
        finally:
            driver.quit()
    
    def scrape_themoviedb(self, title, year=None):
        """从themoviedb获取中文信息"""
        if not self.sources.get('themoviedb', {}).get('enabled', True):
            return {}
        
        api_key = self.sources.get('themoviedb', {}).get('api_key')
        if not api_key:
            self.logger.debug("themoviedb API密钥未配置")
            return {}
        
        base_url = "https://api.themoviedb.org/3"
        
        try:
            # 清理搜索标题
            search_title = self.clean_search_title(title)
            
            self.logger.debug(f"搜索themoviedb: {search_title}")
            
            info = {}
            
            # 搜索多种类型：电影、TV系列
            search_types = [
                ('multi', 'search/multi'),
                ('tv', 'search/tv'),
                ('movie', 'search/movie')
            ]
            
            for search_type, search_endpoint in search_types:
                self.logger.debug(f"搜索 {search_type}: {search_title}")
                
                search_url = f"{base_url}/{search_endpoint}"
                params = {
                    'api_key': api_key,
                    'query': search_title,
                    'language': 'zh-CN',
                    'include_adult': True
                }
                
                if year:
                    if search_type == 'movie':
                        params['year'] = year
                    elif search_type == 'tv':
                        params['first_air_date_year'] = year
                
                response = self.scraper.get(search_url, params=params, headers=self.headers, timeout=10)
                
                if response.status_code != 200:
                    self.logger.debug(f"themoviedb {search_type} 搜索请求失败: {response.status_code}")
                    continue
                
                data = response.json()
                results = data.get('results', [])
                
                if not results:
                    self.logger.debug(f"themoviedb {search_type} 未找到结果")
                    continue
                
                # 选择最佳匹配结果
                best_match = self.find_best_tmdb_match(results, search_title)
                if not best_match:
                    continue
                
                # 获取详细信息
                media_type = best_match.get('media_type', search_type.replace('search/', ''))
                if media_type == 'person':
                    continue
                
                item_id = best_match.get('id')
                if not item_id:
                    continue
                
                # 构建详情URL
                if media_type == 'tv':
                    detail_url = f"{base_url}/tv/{item_id}"
                else:
                    detail_url = f"{base_url}/movie/{item_id}"
                
                detail_params = {
                    'api_key': api_key,
                    'language': 'zh-CN'
                }
                
                detail_response = self.scraper.get(detail_url, params=detail_params, headers=self.headers, timeout=10)
                if detail_response.status_code != 200:
                    continue
                
                detail_data = detail_response.json()
                
                # 提取信息
                info.update(self.extract_tmdb_info(detail_data, media_type))
                
                # 如果是TV系列，尝试获取具体集数信息
                if media_type == 'tv' and 'episode' in search_title.lower():
                    episode_info = self.get_tmdb_episode_info(item_id, search_title, api_key)
                    if episode_info:
                        info.update(episode_info)
                
                if info:
                    self.logger.debug(f"从themoviedb获取到信息: {len(info)} 个字段")
                    break
            
            return info
            
        except Exception as e:
            self.logger.error(f"从themoviedb获取信息失败: {e}")
            return {}
    
    def clean_search_title(self, title):
        """清理搜索标题"""
        # 移除方括号内容
        clean_title = re.sub(r'\[([^\]]*)\]', '', title)
        # 移除网站后缀
        clean_title = re.sub(r'\s*[-–—]\s*H動漫.*$', '', clean_title)
        clean_title = re.sub(r'\s*[-–—]\s*裏番.*$', '', clean_title)
        clean_title = re.sub(r'\s*[-–—]\s*線上看.*$', '', clean_title)
        # 移除集数表示（用于整体搜索）
        clean_title = re.sub(r'\s*第\d+[巻卷].*$', '', clean_title)
        clean_title = re.sub(r'\s*＃\d+.*$', '', clean_title)
        # 清理多余空格
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        return clean_title
    
    def find_best_tmdb_match(self, results, search_title):
        """找到最佳匹配的TMDB结果"""
        if not results:
            return None
        
        # 简单的相似度匹配
        def similarity(a, b):
            a_clean = re.sub(r'[^\w]', '', a.lower())
            b_clean = re.sub(r'[^\w]', '', b.lower())
            if a_clean in b_clean or b_clean in a_clean:
                return 1.0
            return 0.0
        
        best_score = 0
        best_match = None
        
        for result in results[:5]:  # 只检查前5个结果
            # 获取标题
            title_fields = ['title', 'name', 'original_title', 'original_name']
            titles = []
            for field in title_fields:
                if result.get(field):
                    titles.append(result[field])
            
            # 计算最佳匹配分数
            max_score = 0
            for tmdb_title in titles:
                score = similarity(search_title, tmdb_title)
                max_score = max(max_score, score)
            
            if max_score > best_score:
                best_score = max_score
                best_match = result
        
        return best_match if best_score > 0.3 else results[0]  # 如果没有好匹配，返回第一个
    
    def extract_tmdb_info(self, detail_data, media_type):
        """从TMDB详情数据中提取信息"""
        info = {}
        
        # 标题信息
        if media_type == 'tv':
            info['title_cn'] = detail_data.get('name', '')
            info['originaltitle'] = detail_data.get('original_name', '')
        else:
            info['title_cn'] = detail_data.get('title', '')
            info['originaltitle'] = detail_data.get('original_title', '')
        
        # 剧情简介
        overview = detail_data.get('overview', '')
        if overview:
            info['plot'] = f"<![CDATA[{overview}]]>"
            info['outline'] = overview[:200] + "..." if len(overview) > 200 else overview
        
        # 评分
        vote_average = detail_data.get('vote_average')
        if vote_average:
            info['rating'] = str(round(vote_average, 1))
            info['criticrating'] = str(int(vote_average * 10))
        
        # 年份和日期
        if media_type == 'tv':
            first_air_date = detail_data.get('first_air_date', '')
            if first_air_date:
                info['premiered'] = first_air_date
                info['releasedate'] = first_air_date
                info['year'] = first_air_date[:4]
        else:
            release_date = detail_data.get('release_date', '')
            if release_date:
                info['premiered'] = release_date
                info['releasedate'] = release_date
                info['year'] = release_date[:4]
        
        # 时长
        runtime = detail_data.get('runtime') or detail_data.get('episode_run_time')
        if runtime:
            if isinstance(runtime, list):
                runtime = runtime[0] if runtime else 30
            info['runtime'] = str(runtime)
        
        # 类型/标签
        genres = detail_data.get('genres', [])
        if genres:
            genre_names = [g['name'] for g in genres]
            info['genre_tmdb'] = genre_names[:10]
            info['tag_tmdb'] = genre_names[:10]
        
        # 制作公司
        companies = detail_data.get('production_companies', [])
        if companies:
            company_names = [c['name'] for c in companies if c.get('name')]
            if company_names:
                info['studio_tmdb'] = company_names[0]
        
        return info
    
    def get_tmdb_episode_info(self, tv_id, search_title, api_key):
        """获取具体集数的信息"""
        try:
            # 尝试从标题中提取季数和集数
            season_match = re.search(r'第(\d+)[季期]', search_title)
            episode_match = re.search(r'第(\d+)[話话集巻卷]|＃(\d+)', search_title)
            
            season_num = 1  # 默认第1季
            episode_num = 1  # 默认第1集
            
            if season_match:
                season_num = int(season_match.group(1))
            
            if episode_match:
                episode_num = int(episode_match.group(1) or episode_match.group(2))
            
            # 获取具体集数信息
            episode_url = f"https://api.themoviedb.org/3/tv/{tv_id}/season/{season_num}/episode/{episode_num}"
            params = {
                'api_key': api_key,
                'language': 'zh-CN'
            }
            
            response = self.scraper.get(episode_url, params=params, headers=self.headers, timeout=10)
            if response.status_code == 200:
                episode_data = response.json()
                
                episode_info = {}
                
                # 集数标题
                episode_name = episode_data.get('name', '')
                if episode_name:
                    episode_info['title_episode'] = episode_name
                
                # 集数简介
                episode_overview = episode_data.get('overview', '')
                if episode_overview:
                    episode_info['plot'] = f"<![CDATA[{episode_overview}]]>"
                    episode_info['outline'] = episode_overview[:200] + "..." if len(episode_overview) > 200 else episode_overview
                
                # 时长
                runtime = episode_data.get('runtime')
                if runtime:
                    episode_info['runtime'] = str(runtime)
                
                # 播出日期
                air_date = episode_data.get('air_date', '')
                if air_date:
                    episode_info['premiered'] = air_date
                    episode_info['releasedate'] = air_date
                
                return episode_info
        
        except Exception as e:
            self.logger.debug(f"获取集数信息失败: {e}")
        
        return {}
    
    def scrape_getchu(self, title):
        """从getchu.com获取剧照图片和制作商信息"""
        if not self.sources.get('getchu', {}).get('enabled', True):
            return {}
        
        base_url = self.sources.get('getchu', {}).get('base_url', 'https://www.getchu.com')
        
        try:
            # 清理标题用于搜索
            search_title = self.clean_search_title(title)
            self.logger.debug(f"搜索getchu: {search_title}")
            
            # URL编码搜索关键词
            encoded_keyword = quote(search_title.encode('euc-jp'), safe='')
            
            # 构建搜索URL（按照用户提供的格式）
            search_url = f"{base_url}/php/search.phtml"
            params = {
                'genre': 'all',
                'search_keyword': search_title,  # requests会自动处理编码
                'gc': 'gc'
            }
            
            self.logger.debug(f"getchu搜索URL: {search_url}")
            
            # 设置正确的headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }
            
            # 搜索请求
            response = self.scraper.get(search_url, params=params, headers=headers, timeout=15)
            response.encoding = 'euc-jp'  # getchu使用EUC-JP编码
            
            if response.status_code != 200:
                self.logger.warning(f"getchu搜索请求失败: {response.status_code}")
                return {}
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找搜索结果中的详情页链接
            detail_links = []
            
            # 查找带有class="blueb"的链接（根据用户提供的格式）
            links = soup.find_all('a', class_='blueb')
            for link in links:
                href = link.get('href')
                if href and 'soft.phtml?id=' in href:
                    if not href.startswith('http'):
                        href = urljoin(base_url, href)
                    detail_links.append({
                        'url': href,
                        'title': link.get_text(strip=True)
                    })
            
            # 如果没找到blueb链接，尝试其他选择器
            if not detail_links:
                links = soup.find_all('a', href=lambda x: x and 'soft.phtml?id=' in x)
                for link in links:
                    href = link.get('href')
                    if not href.startswith('http'):
                        href = urljoin(base_url, href)
                    detail_links.append({
                        'url': href,
                        'title': link.get_text(strip=True)
                    })
            
            if not detail_links:
                self.logger.debug(f"getchu未找到详情页链接: {search_title}")
                return {}
            
            self.logger.info(f"找到 {len(detail_links)} 个getchu详情页")
            
            # 使用第一个结果获取详细信息
            detail_url = detail_links[0]['url']
            detail_title = detail_links[0]['title']
            
            self.logger.debug(f"访问getchu详情页: {detail_url}")
            
            # 获取详情页内容
            detail_response = self.scraper.get(detail_url, headers=headers, timeout=15)
            detail_response.encoding = 'euc-jp'
            
            if detail_response.status_code != 200:
                self.logger.warning(f"getchu详情页请求失败: {detail_response.status_code}")
                return {}
            
            detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
            
            info = {}
            
            # 1. 获取基本信息
            info['title_getchu'] = detail_title
            
            # 2. 获取制作商信息
            page_text = detail_soup.get_text()
            studio_patterns = [
                (r'ブランド[：:\s]*([^\n\r]+)', 'studio'),
                (r'メーカー[：:\s]*([^\n\r]+)', 'maker'),
                (r'製作[：:\s]*([^\n\r]+)', 'studio'),
                (r'発売元[：:\s]*([^\n\r]+)', 'maker'),
                (r'レーベル[：:\s]*([^\n\r]+)', 'label')
            ]
            
            for pattern, field in studio_patterns:
                match = re.search(pattern, page_text)
                if match:
                    value = match.group(1).strip()
                    # 清理制作商名称
                    value = re.sub(r'\s*\([^)]*\)', '', value)
                    value = re.sub(r'\s*（[^）]*）', '', value)
                    value = re.sub(r'\s+', ' ', value).strip()
                    if value and len(value) > 1:
                        info[field] = value
                        if field == 'studio':
                            info['maker'] = value
                        break
            
            # 3. 获取发售日期
            date_patterns = [
                r'発売日[：:\s]*(\d{4})[年/\-](\d{1,2})[月/\-](\d{1,2})',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
                r'(\d{4})-(\d{1,2})-(\d{1,2})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, page_text)
                if match:
                    year, month, day = match.groups()
                    release_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    info['premiered'] = release_date
                    info['releasedate'] = release_date
                    info['year'] = year
                    break
            
            # 4. 获取样本图像（サンプル画像）
            sample_images = self.extract_sample_images(detail_soup, base_url)
            if sample_images:
                self.logger.info(f"找到 {len(sample_images)} 张样本图像")
                
                # 下载图片
                downloaded_images = self.download_images(sample_images, search_title)
                if downloaded_images:
                    info['poster'] = downloaded_images[0] if downloaded_images else ""
                    info['fanart'] = downloaded_images[0] if downloaded_images else ""
                    info['cover'] = downloaded_images[0] if downloaded_images else ""
                    info['thumb'] = downloaded_images[0] if downloaded_images else ""
                    
                    # 如果有多张图片，设置更多字段
                    if len(downloaded_images) > 1:
                        info['images'] = downloaded_images
            
            # 5. 获取标签信息
            tag_elements = detail_soup.find_all(['span', 'a'], 
                class_=lambda x: x and any(keyword in x.lower() for keyword in ['tag', 'genre', 'category']))
            
            getchu_tags = set()
            for tag_elem in tag_elements:
                tag_text = tag_elem.get_text(strip=True)
                if tag_text and 1 < len(tag_text) < 20:
                    getchu_tags.add(tag_text)
            
            if getchu_tags:
                info['tag_getchu'] = list(getchu_tags)[:10]
            
            self.logger.debug(f"getchu信息: {len(info)} 个字段")
            return info
            
        except Exception as e:
            self.logger.error(f"从getchu获取信息失败: {e}")
            return {}
    
    def extract_sample_images(self, soup, base_url):
        """提取样本图像（サンプル画像）"""
        sample_images = []
        
        try:
            # 查找"サンプル画像"标题
            sample_title = soup.find('div', class_='tabletitle', string=lambda x: x and 'サンプル画像' in x)
            
            if sample_title:
                self.logger.debug("找到サンプル画像区域")
                
                # 从サンプル画像区域开始，查找后续的图片
                current = sample_title.parent
                
                # 向下查找图片，直到找到下一个区域或结束
                while current and len(sample_images) < 10:
                    current = current.find_next_sibling()
                    if not current:
                        break
                    
                    # 如果遇到新的tabletitle，停止搜索
                    if current.find('div', class_='tabletitle'):
                        break
                    
                    # 查找图片
                    imgs = current.find_all('img') if current else []
                    for img in imgs:
                        img_src = img.get('src')
                        if img_src:
                            if not img_src.startswith('http'):
                                img_src = urljoin(base_url, img_src)
                            
                            # 过滤掉小图标
                            if any(keyword in img_src.lower() for keyword in ['icon', 'logo', 'banner', 'button']) or \
                               any(size in img_src for size in ['16x', '32x', '48x']):
                                continue
                            
                            sample_images.append(img_src)
            
            # 如果没找到专门的サンプル画像区域，尝试通用图片选择器
            if not sample_images:
                self.logger.debug("尝试通用图片选择器")
                
                img_selectors = [
                    'img[src*="sample"]',      # 样品图片
                    'img[src*="package"]',     # 包装图片
                    'img[src*="screen"]',      # 截图
                    'img[src*="image"]',       # 通用图片
                    '.sample_image img',       # 样品图片区域
                    '.package_image img'       # 包装图片区域
                ]
                
                for selector in img_selectors:
                    img_elements = soup.select(selector)
                    for img in img_elements:
                        img_src = img.get('src')
                        if img_src:
                            if not img_src.startswith('http'):
                                img_src = urljoin(base_url, img_src)
                            
                            # 过滤小图片和图标
                            if any(keyword in img_src.lower() for keyword in ['icon', 'logo', 'banner', 'button']) or \
                               any(size in img_src for size in ['16x', '32x', '48x']):
                                continue
                            
                            if img_src not in sample_images:
                                sample_images.append(img_src)
                    
                    if sample_images:
                        break
            
        except Exception as e:
            self.logger.debug(f"提取样本图像失败: {e}")
        
        return sample_images[:5]  # 最多返回5张图片
    
    def download_images(self, image_urls, title):
        """下载图片到本地"""
        downloaded = []
        
        # 创建安全的文件名前缀
        safe_title = re.sub(r'[^\w\-_]', '_', title)[:50]
        
        for i, url in enumerate(image_urls[:5]):  # 最多下载5张图片
            try:
                response = requests.get(url, headers=self.headers, timeout=15)
                response.raise_for_status()
                
                # 获取文件扩展名
                parsed_url = urlparse(url)
                ext = Path(parsed_url.path).suffix or '.jpg'
                
                # 生成文件名
                filename = f"{safe_title}_{i+1}{ext}"
                filepath = self.image_dir / filename
                
                # 保存图片
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                downloaded.append(str(filepath))
                self.logger.debug(f"图片下载成功: {filename}")
                
                time.sleep(0.5)  # 避免请求过频繁
                
            except Exception as e:
                self.logger.warning(f"图片下载失败 {url}: {e}")
                continue
        
        return downloaded
    
    def scrape_hanime1_by_title(self, title):
        """基于标题从hanime1.me搜索信息"""
        if not self.sources.get('hanime1', {}).get('enabled', True):
            return {}
        
        base_url = self.sources.get('hanime1', {}).get('base_url', 'https://hanime1.me')
        
        try:
            # 构建搜索URL
            search_url = f"{base_url}/search"
            
            # 清理搜索标题
            search_title = self.clean_search_title(title)
            
            self.logger.debug(f"hanime1搜索标题: {search_title}")
            
            # 使用requests进行搜索
            params = {
                'query': search_title
            }
            
            response = self.scraper.get(search_url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找视频链接
            video_links = []
            
            # 多种选择器尝试
            link_selectors = [
                'a[href*="/watch?v="]',
                'a[href*="watch"]',
                '.video-item a',
                '.card a'
            ]
            
            for selector in link_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href and '/watch?v=' in href:
                        if not href.startswith('http'):
                            href = f"{base_url}{href}"
                        video_links.append(href)
                
                if video_links:
                    break
            
            if not video_links:
                self.logger.debug(f"基于标题未找到视频: {search_title}")
                return {}
            
            # 使用第一个结果
            first_video_url = video_links[0]
            
            # 提取视频ID
            video_id_match = re.search(r'v=(\d+)', first_video_url)
            if video_id_match:
                video_id = video_id_match.group(1)
                self.logger.debug(f"通过标题搜索找到视频ID: {video_id}")
                
                # 使用视频ID获取详细信息
                return self.scrape_hanime1(video_id)
            
            return {}
            
        except Exception as e:
            self.logger.error(f"基于标题搜索hanime1失败: {e}")
            return {}
    
    def merge_info(self, *info_dicts):
        """合并多个信息字典，优先级: hanime1 > themoviedb > getchu"""
        merged = {}
        
        # 合并所有字典
        all_info = {}
        for info_dict in info_dicts:
            if info_dict:
                all_info.update(info_dict)
        
        if not all_info:
            return merged
        
        # 定义字段优先级映射
        field_priority = {
            'title': ['title', 'title_cn', 'title_jp', 'title_rm'],
            'originaltitle': ['originaltitle', 'title_jp', 'title'],
            'plot': ['plot', 'plot_cn'],
            'outline': ['outline'],
            'rating': ['rating'],
            'criticrating': ['criticrating'],
            'year': ['year'],
            'premiered': ['premiered'],
            'releasedate': ['releasedate'],
            'runtime': ['runtime'],
            'studio': ['studio', 'studio_tmdb', 'maker'],
            'maker': ['maker', 'studio'],
            'tag': ['tag', 'tag_tmdb', 'tag_getchu'],
            'genre': ['genre', 'genre_tmdb', 'tag'],
            'customrating': ['customrating'],
            'mpaa': ['mpaa', 'customrating'],
            'num': ['num'],
            'poster': ['poster'],
            'fanart': ['fanart'],
            'thumb': ['thumb'],
            'cover': ['cover'],
            'website': ['website'],
            'hanime1': ['hanime1'],
            'uncensored': ['uncensored'],
            'title_cn': ['title_cn', 'title'],
            'title_jp': ['title_jp', 'originaltitle'],
            'title_rm': ['title_rm', 'title']
        }
        
        # 按优先级选择字段值
        for target_field, source_fields in field_priority.items():
            for source_field in source_fields:
                if source_field in all_info and all_info[source_field]:
                    value = all_info[source_field]
                    
                    # 特殊处理某些字段
                    if target_field == 'title':
                        # 确保标题被清理过
                        if isinstance(value, str):
                            value = self.clean_final_title(value)
                    
                    if target_field in ['tag', 'genre'] and isinstance(value, list):
                        # 去重并限制数量
                        unique_values = []
                        for v in value:
                            if v not in unique_values and len(unique_values) < 15:
                                unique_values.append(v)
                        value = unique_values
                    
                    merged[target_field] = value
                    break
        
        # 补充必要的默认值
        defaults = {
            'customrating': '里番',
            'mpaa': '里番',
            'lockdata': 'false',
            'uncensored': 'False',
            'dateadded': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        for key, default_value in defaults.items():
            if key not in merged:
                merged[key] = default_value
        
        # 确保基本信息完整性
        if not merged.get('title'):
            merged['title'] = '未知标题'
        
        if not merged.get('rating'):
            merged['rating'] = '7.0'
            merged['criticrating'] = '70'
        
        if not merged.get('runtime'):
            merged['runtime'] = '30'
        
        if not merged.get('year'):
            merged['year'] = datetime.now().year
            merged['premiered'] = f"{merged['year']}-01-01"
            merged['releasedate'] = f"{merged['year']}-01-01"
        
        return merged
    
    def clean_final_title(self, title):
        """清理最终标题"""
        if not title:
            return title
        
        # 移除网站后缀
        clean_title = re.sub(r'\s*[-–—]\s*H動漫.*$', '', title)
        clean_title = re.sub(r'\s*[-–—]\s*裏番.*$', '', clean_title)
        clean_title = re.sub(r'\s*[-–—]\s*線上看.*$', '', clean_title)
        clean_title = re.sub(r'\s*[-–—]\s*在线观看.*$', '', clean_title)
        clean_title = re.sub(r'\s*[-–—]\s*免费观看.*$', '', clean_title)
        
        # 移除方括号内容（但保留某些重要信息）
        # 保留OVA、剧场版等重要标识
        important_brackets = re.findall(r'\[(OVA|剧场版|TV|Movie|完全版|无修正|未删减)\]', clean_title, re.IGNORECASE)
        clean_title = re.sub(r'\[([^\]]*)\]', '', clean_title)
        
        # 重新添加重要的方括号内容
        for bracket in important_brackets:
            clean_title = f"{clean_title.strip()} [{bracket}]"
        
        # 清理多余空格
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        return clean_title
    
    def create_nfo_content(self, info):
        """创建完整的NFO文件内容（增强版）"""
        # 创建XML结构
        root = ET.Element('movie')
        
        # 1. 基本信息字段
        basic_fields = [
            ('plot', info.get('plot', '')),
            ('outline', info.get('outline', '')),
            ('customrating', info.get('customrating', '里番')),
            ('lockdata', info.get('lockdata', 'false')),
            ('dateadded', info.get('dateadded', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))),
            ('title', info.get('title', '')),
            ('originaltitle', info.get('originaltitle', info.get('title', ''))),
            ('rating', info.get('rating', '7.5')),
            ('year', info.get('year', '')),
            ('sorttitle', info.get('title', '')),
            ('mpaa', info.get('mpaa', '里番')),
            ('premiered', info.get('premiered', '')),
            ('releasedate', info.get('releasedate', '')),
            ('criticrating', info.get('criticrating', '75')),
            ('runtime', info.get('runtime', '30')),
            ('tagline', info.get('title', ''))
        ]
        
        for field, value in basic_fields:
            if value:
                elem = ET.SubElement(root, field)
                if field == 'plot' and not value.startswith('<![CDATA['):
                    elem.text = f"<![CDATA[{value}]]>"
                else:
                    elem.text = str(value)
        
        # 2. 标签和类型（重复元素）
        tags = info.get('tag', [])
        genres = info.get('genre', [])
        
        # 添加标签
        for tag in tags:
            tag_elem = ET.SubElement(root, 'tag')
            tag_elem.text = str(tag)
        
        # 添加类型
        for genre in genres:
            genre_elem = ET.SubElement(root, 'genre')
            genre_elem.text = str(genre)
        
        # 3. 制作信息
        if info.get('studio'):
            studio_elem = ET.SubElement(root, 'studio')
            studio_elem.text = info['studio']
        
        # 4. 系列信息（set）
        if info.get('title'):
            set_elem = ET.SubElement(root, 'set')
            set_name = ET.SubElement(set_elem, 'name')
            # 提取系列名称（去掉前后编号）
            series_name = re.sub(r'\s*[前後中]編.*$', '', info['title'])
            series_name = re.sub(r'\s*第\d+[話话集巻].*$', '', series_name)
            series_name = re.sub(r'\s*～.*～$', '', series_name)
            set_name.text = series_name.strip()
        
        # 5. 文件信息（fileinfo） - 创建一个基础的文件信息结构
        fileinfo_elem = ET.SubElement(root, 'fileinfo')
        streamdetails_elem = ET.SubElement(fileinfo_elem, 'streamdetails')
        
        # 视频流信息
        video_elem = ET.SubElement(streamdetails_elem, 'video')
        video_fields = [
            ('codec', 'h264'),
            ('micodec', 'h264'),
            ('bitrate', '2500000'),
            ('width', '1920'),
            ('height', '1080'),
            ('aspect', '16:9'),
            ('aspectratio', '16:9'),
            ('framerate', '23.976025'),
            ('scantype', 'progressive'),
            ('default', 'True'),
            ('forced', 'False'),
            ('duration', info.get('runtime', '30')),
            ('durationinseconds', str(int(info.get('runtime', '30')) * 60))
        ]
        
        for field, value in video_fields:
            elem = ET.SubElement(video_elem, field)
            elem.text = str(value)
        
        # 音频流信息
        audio_elem = ET.SubElement(streamdetails_elem, 'audio')
        audio_fields = [
            ('codec', 'aac'),
            ('micodec', 'aac'),
            ('bitrate', '192000'),
            ('scantype', 'progressive'),
            ('channels', '2'),
            ('samplingrate', '48000'),
            ('default', 'True'),
            ('forced', 'False')
        ]
        
        for field, value in audio_fields:
            elem = ET.SubElement(audio_elem, field)
            elem.text = str(value)
        
        # 字幕信息（中文字幕）
        for i in range(2):  # 添加2个字幕轨道
            subtitle_elem = ET.SubElement(streamdetails_elem, 'subtitle')
            subtitle_fields = [
                ('codec', 'ass'),
                ('micodec', 'ass'),
                ('language', 'chi'),
                ('scantype', 'progressive'),
                ('default', 'False'),
                ('forced', 'False')
            ]
            
            for field, value in subtitle_fields:
                elem = ET.SubElement(subtitle_elem, field)
                elem.text = str(value)
        
        # 附件信息（字体等）
        for i in range(8):  # 添加8个附件
            attachment_elem = ET.SubElement(streamdetails_elem, 'attachment')
            attachment_fields = [
                ('scantype', 'progressive'),
                ('default', 'False'),
                ('forced', 'False')
            ]
            
            for field, value in attachment_fields:
                elem = ET.SubElement(attachment_elem, field)
                elem.text = str(value)
        
        # 6. 图片字段
        image_fields = ['poster', 'thumb', 'fanart']
        for field in image_fields:
            elem = ET.SubElement(root, field)
            elem.text = info.get(field, '')
        
        # 7. 扩展字段
        extended_fields = [
            ('title_cn', info.get('title_cn', info.get('title', ''))),
            ('maker', info.get('maker', info.get('studio', ''))),
            ('label', ''),
            ('num', info.get('num', '')),
            ('release', info.get('releasedate', '')),
            ('cover', info.get('cover', '')),
            ('website', info.get('website', '')),
            ('hanime1', info.get('hanime1', '')),
            ('uncensored', info.get('uncensored', 'False')),
            ('title_jp', info.get('title_jp', info.get('title', ''))),
            ('title_rm', info.get('title_rm', info.get('title', '')))
        ]
        
        for field, value in extended_fields:
            elem = ET.SubElement(root, field)
            elem.text = str(value)
        
        # 生成XML字符串
        xml_str = ET.tostring(root, encoding='unicode', method='xml')
        
        # 添加XML声明和格式化
        formatted_xml = '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n'
        formatted_xml += self.format_xml(xml_str)
        
        return formatted_xml
    
    def format_xml(self, xml_str):
        """格式化XML字符串（增强版）"""
        try:
            import xml.dom.minidom
            dom = xml.dom.minidom.parseString(xml_str)
            pretty_xml = dom.toprettyxml(indent="  ", encoding=None)
            
            # 清理格式化后的XML
            lines = pretty_xml.split('\n')
            clean_lines = []
            
            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('<?xml'):
                    clean_lines.append(line)
            
            return '\n'.join(clean_lines)
            
        except Exception as e:
            self.logger.warning(f"XML格式化失败: {e}")
            return xml_str
    
    def save_nfo_file(self, video_file_path, nfo_content):
        """保存NFO文件（增强版）"""
        video_path = Path(video_file_path)
        nfo_path = video_path.with_suffix('.nfo')
        
        # 检查是否跳过已存在的NFO文件
        if nfo_path.exists() and self.scraping_config.get('settings', {}).get('skip_existing_nfo', True):
            self.logger.info(f"NFO文件已存在，跳过: {nfo_path.name}")
            return True
        
        try:
            encoding = self.nfo_config.get('encoding', 'utf-8')
            
            # 处理CDATA部分
            content_to_save = nfo_content
            
            # 修复CDATA格式
            content_to_save = re.sub(
                r'&lt;!\[CDATA\[(.*?)\]\]&gt;',
                r'<![CDATA[\1]]>',
                content_to_save,
                flags=re.DOTALL
            )
            
            with open(nfo_path, 'w', encoding=encoding) as f:
                f.write(content_to_save)
            
            self.logger.info(f"✓ NFO文件已保存: {nfo_path.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存NFO文件失败 {nfo_path}: {e}")
            return False
    
    def scrape_video_file(self, video_file_path, offline_mode=False):
        """刮削单个视频文件（增强版，支持标题搜索）"""
        video_path = Path(video_file_path)
        if not video_path.exists():
            self.logger.warning(f"视频文件不存在: {video_path}")
            return False
        
        self.logger.info(f"开始刮削视频文件: {video_path.name}")
        
        # 尝试从文件名提取视频ID
        video_id = self.extract_video_id_from_filename(video_path.name)
        
        # 准备搜索标题
        title_for_search = video_path.stem  # 使用文件名作为标题
        
        if video_id:
            self.logger.info(f"提取到视频ID: {video_id}")
            search_mode = "id_based"
        else:
            self.logger.info(f"未提取到视频ID，将使用标题搜索: {title_for_search}")
            search_mode = "title_based"
        
        if offline_mode:
            self.logger.info("离线模式：基于文件名生成基础NFO信息")
            return self.create_offline_nfo(video_path, title_for_search)
        
        # 从不同数据源获取信息
        info_sources = []
        
        # 1. 从hanime1.me获取信息
        self.logger.info("正在从 hanime1.me 获取信息...")
        if search_mode == "id_based":
            hanime_info = self.scrape_hanime1(video_id)
        else:
            hanime_info = self.scrape_hanime1_by_title(title_for_search)
        
        if hanime_info:
            info_sources.append(hanime_info)
            self.logger.info(f"hanime1.me: 获取到 {len(hanime_info)} 个字段")
            # 更新搜索标题
            if hanime_info.get('title'):
                title_for_search = hanime_info['title']
        else:
            self.logger.warning("hanime1.me: 未获取到信息")
        
        # 2. 从getchu获取图片和制作商信息
        self.logger.info("正在从 getchu.com 获取图片和制作商信息...")
        getchu_info = self.scrape_getchu(title_for_search)
        if getchu_info:
            info_sources.append(getchu_info)
            self.logger.info(f"getchu: 获取到 {len(getchu_info)} 个字段")
        else:
            self.logger.info("getchu: 未找到匹配信息")
        
        if not info_sources:
            self.logger.warning(f"所有数据源都未获取到信息，使用基础信息: {video_path.name}")
            return self.create_basic_nfo(video_path, title_for_search)
        
        # 合并信息
        self.logger.info("正在合并多源信息...")
        merged_info = self.merge_info(*info_sources)
        
        # 确保基本信息完整
        if not merged_info.get('title'):
            merged_info['title'] = title_for_search
        
        # 记录合并结果
        self.logger.info(f"信息合并完成: 共 {len(merged_info)} 个字段")
        self.logger.debug(f"合并后的字段: {list(merged_info.keys())}")
        
        # 创建NFO内容
        self.logger.info("正在生成NFO文件...")
        nfo_content = self.create_nfo_content(merged_info)
        
        # 保存NFO文件
        success = self.save_nfo_file(video_path, nfo_content)
        
        if success:
            self.logger.info(f"✅ 刮削完成: {video_path.name}")
        else:
            self.logger.error(f"❌ 刮削失败: {video_path.name}")
        
        return success
    
    def create_offline_nfo(self, video_path, title):
        """创建离线NFO（基于文件名的基础信息）"""
        self.logger.info(f"创建离线NFO: {title}")
        
        # 创建基础信息
        info = {
            'title': title,
            'title_cn': title,
            'title_jp': title,
            'title_rm': title,
            'originaltitle': title,
            'plot': f"<![CDATA[{title}]]>",
            'outline': title,
            'customrating': '里番',
            'mpaa': '里番',
            'rating': '7.0',
            'criticrating': '70',
            'runtime': '30',
            'year': '2024',  # 默认年份
            'premiered': '2024-01-01',
            'releasedate': '2024-01-01',
            'dateadded': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lockdata': 'false',
            'uncensored': 'False',
            'tag': ['里番', '动画'],
            'genre': ['里番', '动画']
        }
        
        # 尝试从标题提取一些信息
        if '第' in title and ('話' in title or '话' in title or '集' in title):
            info['tag'].append('连载')
            info['genre'].append('连载')
        
        if any(keyword in title for keyword in ['OVA', 'ova']):
            info['tag'].append('OVA')
            info['genre'].append('OVA')
        
        # 生成NFO内容
        nfo_content = self.create_nfo_content(info)
        
        # 保存NFO文件
        success = self.save_nfo_file(video_path, nfo_content)
        
        if success:
            self.logger.info(f"✅ 离线NFO创建完成: {video_path.name}")
        else:
            self.logger.error(f"❌ 离线NFO创建失败: {video_path.name}")
        
        return success
    
    def create_basic_nfo(self, video_path, title):
        """创建基础NFO（当所有数据源都失败时）"""
        self.logger.info(f"创建基础NFO: {title}")
        
        # 创建最小化信息
        info = {
            'title': title,
            'title_cn': title,
            'title_jp': title,
            'title_rm': title,
            'originaltitle': title,
            'plot': f"<![CDATA[{title}的相关内容。]]>",
            'outline': f"{title}的相关内容。",
            'customrating': '里番',
            'mpaa': '里番',
            'rating': '6.5',
            'criticrating': '65',
            'runtime': '30',
            'year': '2024',
            'premiered': '2024-01-01',
            'releasedate': '2024-01-01',
            'dateadded': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lockdata': 'false',
            'uncensored': 'False',
            'tag': ['里番'],
            'genre': ['里番']
        }
        
        # 生成NFO内容
        nfo_content = self.create_nfo_content(info)
        
        # 保存NFO文件
        success = self.save_nfo_file(video_path, nfo_content)
        
        if success:
            self.logger.info(f"✅ 基础NFO创建完成: {video_path.name}")
        else:
            self.logger.error(f"❌ 基础NFO创建失败: {video_path.name}")
        
        return success
    
    def find_video_files(self, directory):
        """查找目录中的视频文件"""
        video_extensions = self.download_config.get('file_formats', {}).get('video', ['.mp4', '.mkv', '.avi', '.mov', '.wmv'])
        
        video_files = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            self.logger.warning(f"目录不存在: {directory}")
            return video_files
        
        for ext in video_extensions:
            video_files.extend(directory_path.glob(f"*{ext}"))
            # 不递归查找，只查找当前目录
        
        return video_files
    
    def scrape_directory(self, directory, offline_mode=False):
        """刮削目录中的所有视频文件"""
        directory = Path(directory)
        if not directory.exists():
            self.logger.error(f"目录不存在: {directory}")
            return 0
        
        self.logger.info(f"开始刮削目录: {directory}")
        
        video_files = self.find_video_files(directory)
        if not video_files:
            self.logger.info("未找到视频文件")
            return 0
        
        self.logger.info(f"找到 {len(video_files)} 个视频文件")
        
        success_count = 0
        for video_file in video_files:
            try:
                if self.scrape_video_file(video_file, offline_mode=offline_mode):
                    success_count += 1
                time.sleep(1)  # 避免请求过快
            except Exception as e:
                self.logger.error(f"刮削文件失败 {video_file}: {e}")
        
        self.logger.info(f"刮削完成: 成功 {success_count}/{len(video_files)}")
        
        if success_count < len(video_files):
            self.logger.warning("⚠ 部分文件刮削失败")
        
        return success_count
    
    def scrape_all(self, offline_mode=False):
        """刮削所有配置的目录"""
        downloads_dir = Path(self.config['download']['download_dir'])
        
        # 获取当前配置的年月
        current_year = self.config['crawl']['date_filter'].get('year', datetime.now().year)
        current_month = self.config['crawl']['date_filter'].get('month', datetime.now().month)
        
        # 构建目标目录
        target_dir = downloads_dir / str(current_year) / f"{current_month:02d}"
        
        self.logger.info(f"开始刮削目录: {target_dir}")
        return self.scrape_directory(target_dir, offline_mode=offline_mode)

# 保持向后兼容
if __name__ == "__main__":
    # 简单的测试代码
    import yaml
    import logging
    
    # 加载配置
    with open('config.yml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建刮削器并运行
    scraper = HanimeScraper(config, logger)
    scraper.scrape_all()

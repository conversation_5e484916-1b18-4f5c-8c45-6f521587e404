#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频处理系统 - 故障排除工具
诊断常见问题并提供解决方案
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    系统故障排除工具                          ║
║                   System Troubleshooter                       ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        'requests', 'PyYAML', 'beautifulsoup4', 'lxml', 
        'selenium', 'cloudscraper'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_chrome_driver():
    """检查Chrome和ChromeDriver"""
    print("\n🔍 检查Chrome浏览器和ChromeDriver...")
    
    # 检查Chrome浏览器
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if Path(path).exists():
            print(f"   ✅ Chrome浏览器: {path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("   ❌ 未找到Chrome浏览器")
        print("   💡 请安装Chrome浏览器: https://www.google.com/chrome/")
    
    # 检查ChromeDriver
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("   ✅ ChromeDriver可用")
        return True
        
    except Exception as e:
        print(f"   ❌ ChromeDriver问题: {e}")
        print("   💡 解决方案:")
        print("      1. 下载ChromeDriver: https://chromedriver.chromium.org/")
        print("      2. 将chromedriver.exe放到系统PATH中")
        print("      3. 或者安装webdriver-manager: pip install webdriver-manager")
        return False

def check_config_file():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_path = Path("config.yml")
    if not config_path.exists():
        print("   ❌ config.yml不存在")
        return False
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查主要配置项
        required_sections = ['crawl', 'download', 'rename', 'scraping']
        for section in required_sections:
            if section in config:
                print(f"   ✅ {section}配置存在")
            else:
                print(f"   ❌ {section}配置缺失")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"   ❌ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 读取配置文件失败: {e}")
        return False

def check_network():
    """检查网络连接"""
    print("\n🔍 检查网络连接...")
    
    test_urls = [
        "https://www.google.com",
        "https://hanime1.me",
        "https://api.themoviedb.org",
        "https://www.getchu.com"
    ]
    
    try:
        import requests
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ {url}")
                else:
                    print(f"   ⚠️ {url} - 状态码: {response.status_code}")
            except requests.exceptions.Timeout:
                print(f"   ⚠️ {url} - 连接超时")
            except requests.exceptions.ConnectionError:
                print(f"   ❌ {url} - 连接失败")
            except Exception as e:
                print(f"   ❌ {url} - 错误: {e}")
        
        return True
        
    except ImportError:
        print("   ❌ requests模块未安装")
        return False

def check_permissions():
    """检查文件权限"""
    print("\n🔍 检查文件权限...")
    
    # 检查当前目录写入权限
    test_file = Path("test_permission.tmp")
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        test_file.unlink()
        print("   ✅ 当前目录可写")
    except Exception as e:
        print(f"   ❌ 当前目录不可写: {e}")
        return False
    
    # 检查下载目录
    try:
        import yaml
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        download_dir = Path(config.get('download', {}).get('download_dir', './downloads'))
        download_dir.mkdir(parents=True, exist_ok=True)
        
        test_file = download_dir / "test_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        test_file.unlink()
        print(f"   ✅ 下载目录可写: {download_dir}")
        
    except Exception as e:
        print(f"   ❌ 下载目录权限问题: {e}")
        return False
    
    return True

def check_ssl_issues():
    """检查SSL问题"""
    print("\n🔍 检查SSL配置...")
    
    try:
        import ssl
        import requests
        
        # 测试SSL连接
        test_url = "https://hanime1.me"
        
        # 正常请求
        try:
            response = requests.get(test_url, timeout=10)
            print("   ✅ SSL连接正常")
            return True
        except requests.exceptions.SSLError as e:
            print(f"   ⚠️ SSL错误: {e}")
            print("   💡 这通常是正常的，程序会自动处理SSL问题")
            return True
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
            return False
            
    except ImportError:
        print("   ❌ ssl模块不可用")
        return False

def generate_test_config():
    """生成测试配置文件"""
    print("\n🔧 生成测试配置文件...")
    
    test_config = """# 测试配置文件
crawl:
  base_url: "https://hanime1.me"
  search_url: "https://hanime1.me/search"
  search_params:
    genre: "%E8%A3%8F%E7%95%AA"
    type: ""
    sort: ""
    query: ""
  date_filter:
    enabled: true
    year: 2025
    month: 1
    day: 0
  settings:
    max_pages: 10
    delay_between_requests: 5
    timeout: 30
    retry_times: 5
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

download:
  download_dir: "./downloads"
  temp_dir: "./temp"
  organize_by_date: true
  settings:
    max_concurrent: 3
    chunk_size: 1024
    timeout: 300
    retry_times: 3
    resume_download: true
    quality_priority: ["1080", "720", "480"]

rename:
  rules:
    remove_brackets: true
    remove_parentheses: false
    trim_spaces: true
    replace_illegal_chars: true
    remove_website_suffixes: true
  templates:
    default: "{title}"
  current_template: "default"

scraping:
  sources:
    hanime1:
      enabled: true
      base_url: "https://hanime1.me"
    themoviedb:
      enabled: false
      api_key: ""
    getchu:
      enabled: true
      base_url: "https://www.getchu.com"

logging:
  level: "INFO"
  file: "scraper.log"
"""
    
    try:
        with open("config_test.yml", 'w', encoding='utf-8') as f:
            f.write(test_config)
        print("   ✅ 测试配置文件已生成: config_test.yml")
        return True
    except Exception as e:
        print(f"   ❌ 生成配置文件失败: {e}")
        return False

def run_simple_test():
    """运行简单测试"""
    print("\n🧪 运行简单功能测试...")
    
    try:
        # 测试重命名功能
        from rename import HanimeRenamer
        import yaml
        
        # 使用测试配置
        config_file = "config_test.yml" if Path("config_test.yml").exists() else "config.yml"
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        class DummyLogger:
            def debug(self, msg): pass
            def info(self, msg): pass
            def warning(self, msg): pass
            def error(self, msg): pass
        
        renamer = HanimeRenamer(config, DummyLogger())
        
        # 测试标题清理
        test_title = "测试视频 [中文字幕] - H動漫／裏番／線上看"
        cleaned = renamer.clean_title(test_title)
        
        if cleaned and cleaned.strip():
            print(f"   ✅ 重命名功能正常")
            print(f"      原始: {test_title}")
            print(f"      清理: {cleaned}")
        else:
            print(f"   ❌ 重命名功能异常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    print("开始系统诊断...\n")
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("Chrome和ChromeDriver", check_chrome_driver),
        ("配置文件", check_config_file),
        ("网络连接", check_network),
        ("文件权限", check_permissions),
        ("SSL配置", check_ssl_issues),
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"   ❌ 检查{name}时出错: {e}")
            results.append((name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("诊断结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    # 如果有失败项，提供解决建议
    if passed < total:
        print("\n🔧 建议的解决步骤:")
        print("-" * 60)
        print("1. 安装缺失的依赖包:")
        print("   pip install -r requirements.txt")
        print("\n2. 安装Chrome浏览器和ChromeDriver:")
        print("   https://www.google.com/chrome/")
        print("   https://chromedriver.chromium.org/")
        print("\n3. 生成测试配置文件:")
        if input("\n是否生成测试配置文件? (y/n): ").lower() == 'y':
            generate_test_config()
        print("\n4. 运行简单测试:")
        if input("是否运行简单测试? (y/n): ").lower() == 'y':
            run_simple_test()
    else:
        print("\n🎉 所有检查都通过了！系统应该可以正常运行。")
        print("如果仍有问题，请检查网络连接和防火墙设置。")

if __name__ == "__main__":
    main() 
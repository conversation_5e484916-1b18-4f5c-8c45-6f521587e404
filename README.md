# Hanime1.me 视频抓取处理系统

一个功能完整的视频下载、重命名和刮削系统，专门用于处理 hanime1.me 网站的视频内容。

## 功能特性

- 🔍 **智能抓取**: 从 hanime1.me 搜索和下载视频
- 📁 **自动组织**: 按年月自动组织下载目录
- 🏷️ **智能重命名**: 自动获取标题并去除方括号内容
- 📋 **多源刮削**: 支持从多个数据源获取元数据
- 📄 **NFO 生成**: 自动生成媒体中心兼容的 NFO 文件
- ⚙️ **配置灵活**: 通过 YAML 配置文件轻松自定义
- 🚀 **断点续传**: 支持下载中断后继续

## 支持的数据源

- **hanime1.me**: 主要视频源和基础信息
- **themoviedb.org**: 电影数据库信息补充
- **getchu.com**: 日本成人游戏信息

## 系统要求

- Python 3.8+
- Chrome/Chromium 浏览器
- ChromeDriver

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 配置系统

编辑 `config.yml` 文件，主要配置项：

```yaml
# 抓取配置
crawl:
  date_filter:
    year: 2025 # 抓取年份
    month: 1 # 抓取月份

# 下载配置
download:
  download_dir: "./downloads" # 下载目录
  organize_by_date: true # 按日期组织目录

# 重命名配置
rename:
  rules:
    remove_brackets: true # 去掉方括号内容

# 刮削配置
scraping:
  sources:
    hanime1:
      enabled: true
    themoviedb:
      enabled: true
      api_key: "" # 需要申请API密钥
    getchu:
      enabled: true
```

### 2. 运行程序

#### 方式一：交互式界面（推荐）

```bash
python quick_start.py
```

#### 方式二：命令行模式

```bash
# 完整流程
python main.py -m all -y 2025 -M 1

# 仅下载
python main.py -m crawl -y 2025 -M 1

# 仅重命名
python main.py -m rename

# 仅刮削
python main.py -m scrape
```

## 配置说明

### 抓取配置 (crawl)

- `date_filter`: 时间筛选
  - `year`: 年份，0 表示不限制
  - `month`: 月份，0 表示不限制
- `settings`: 抓取设置
  - `delay_between_requests`: 请求间隔（秒）
  - `retry_times`: 重试次数
  - `user_agent`: 用户代理

### 下载配置 (download)

- `download_dir`: 下载目录路径
- `temp_dir`: 临时文件目录
- `organize_by_date`: 是否按年月组织目录
- `settings`: 下载设置
  - `quality_priority`: 视频质量优先级 ["1080", "720", "480"]
  - `resume_download`: 是否支持断点续传
  - `max_concurrent`: 最大并发下载数

### 重命名配置 (rename)

- `rules`: 重命名规则
  - `remove_brackets`: 去掉方括号内容
  - `remove_parentheses`: 去掉圆括号内容
  - `trim_spaces`: 去掉多余空格
  - `replace_illegal_chars`: 替换非法字符
- `duplicate_handling`: 重复文件处理方式
  - `skip`: 跳过
  - `overwrite`: 覆盖
  - `rename`: 重命名

### 刮削配置 (scraping)

- `sources`: 数据源配置
  - `hanime1`: hanime1.me 配置
  - `themoviedb`: themoviedb.org 配置（需要 API 密钥）
  - `getchu`: getchu.com 配置
- `nfo`: NFO 文件配置
  - `encoding`: 文件编码
  - `skip_existing_nfo`: 跳过已存在的 NFO 文件

## 使用示例

### 示例 1: 下载 2025 年 1 月的视频

1. 编辑 `config.yml`：
   ```yaml
   crawl:
     date_filter:
       year: 2025
       month: 1
   ```
2. 运行：`python quick_start.py` 选择选项 1

### 示例 2: 仅对现有文件重命名

运行：`python main.py -m rename`

### 示例 3: 为现有视频生成 NFO 文件

运行：`python main.py -m scrape`

## 目录结构

```
downloads/
├── 2025/
│   ├── 01/
│   │   ├── OVA イクイク サキュバス再教育 ＃2.mp4
│   │   ├── OVA イクイク サキュバス再教育 ＃2.nfo
│   │   └── ...
│   └── 02/
│       └── ...
└── 2024/
    └── ...
```

## 文件命名示例

- **原始文件名**: `102260-1080p.mp4`
- **重命名后**: `OVA イクイク サキュバス再教育 ＃2.mp4`
- **NFO 文件**: `OVA イクイク サキュバス再教育 ＃2.nfo`

## 注意事项

1. **Chrome 浏览器**: 需要安装 Chrome 浏览器和对应版本的 ChromeDriver
2. **网络环境**: 确保能正常访问目标网站
3. **API 密钥**: themoviedb 功能需要申请 API 密钥
4. **请求频率**: 程序已内置延迟，避免过于频繁的请求
5. **存储空间**: 确保有足够的磁盘空间存储下载的视频文件

## 故障排除

### Chrome WebDriver 问题

```bash
# Ubuntu/Debian
sudo apt-get install chromium-browser chromium-chromedriver

# 或手动下载ChromeDriver到系统PATH
```

### 网络连接问题

- 检查网络连接
- 配置代理设置（如需要）
- 调整请求超时时间

### 权限问题

- 确保下载目录有写入权限
- 检查文件系统空间

## 日志

程序运行日志保存在 `scraper.log` 文件中，可以查看详细的执行情况和错误信息。

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 贡献

欢迎提交 Issues 和 Pull Requests 来改进这个项目！

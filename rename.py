#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频重命名模块
"""

import os
import re
import time
from pathlib import Path
from urllib.parse import urlparse, parse_qs
import requests
from bs4 import BeautifulSoup
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException

class HanimeRenamer:
    """Hanime1.me 视频重命名器"""
    
    def __init__(self, config, logger):
        """初始化重命名器"""
        self.config = config
        self.logger = logger
        self.scraper = cloudscraper.create_scraper()
        
        # 从配置获取参数
        self.rename_config = config.get('rename', {})
        self.download_config = config.get('download', {})
        self.crawl_config = config.get('crawl', {})
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.crawl_config.get('settings', {}).get('user_agent', 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        }
        
        # 字符替换规则
        self.char_replacements = self.rename_config.get('char_replacements', {
            "/": "／", "\\": "＼", ":": "：", "*": "＊", "?": "？",
            '"': "＂", "<": "＜", ">": "＞", "|": "｜"
        })
    
    def extract_video_id_from_filename(self, filename):
        """从文件名提取视频ID"""
        # 优先查找特定模式的视频ID
        
        # 模式1: 纯数字ID，通常是6位数 (如: 102260-1080p.mp4 -> 102260)
        match = re.search(r'(\d{6,8})', filename)
        if match:
            return match.group(1)
        
        # 模式2: Video 后跟数字 (如: Video 101451_101451.mp4)
        match = re.search(r'Video\s*(\d+)', filename, re.IGNORECASE)
        if match:
            return match.group(1)
        
        # 模式3: 文件名开头的长数字ID
        match = re.search(r'^(\d{5,})', filename)
        if match:
            return match.group(1)
        
        # 模式4: 下划线或横线分隔的数字ID
        match = re.search(r'[-_](\d{5,})[-_]', filename)
        if match:
            return match.group(1)
        
        # 最后尝试: 任何5位以上的数字
        match = re.search(r'(\d{5,})', filename)
        if match:
            return match.group(1)
        
        # 如果都没找到，记录警告并返回None
        self.logger.warning(f"无法从文件名提取有效的视频ID: {filename}")
        return None
    
    def build_hanime_url(self, video_id):
        """根据视频ID构建hanime1.me URL"""
        base_url = self.crawl_config.get('base_url', 'https://hanime1.me')
        return f"{base_url}/watch?v={video_id}"
    
    def setup_webdriver(self):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        
        # 最强SSL绕过设置
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        chrome_options.add_argument('--ignore-ssl-errors-accept-any-certs')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-javascript')
        chrome_options.add_argument('--accept-insecure-certs')
        chrome_options.add_argument('--allow-insecure-localhost')
        chrome_options.add_argument('--disable-tls13')
        chrome_options.add_argument('--ssl-version-fallback-min=tls1')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        
        # 禁用更多网络功能以减少SSL问题
        chrome_options.add_argument('--disable-background-networking')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--disable-translate')
        chrome_options.add_argument('--disable-ipc-flooding-protection')
        
        chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            # 设置更短的超时
            driver.set_page_load_timeout(15)
            driver.implicitly_wait(3)
            return driver
        except WebDriverException as e:
            self.logger.error(f"Chrome WebDriver 初始化失败: {e}")
            return None
    
    def get_title_from_hanime(self, video_url):
        """从hanime1.me获取视频标题"""
        self.logger.debug(f"获取标题: {video_url}")
        
        driver = self.setup_webdriver()
        if not driver:
            return None
        
        try:
            driver.get(video_url)
            driver.implicitly_wait(5)
            page_content = driver.page_source
            
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # 尝试多种方式获取标题
            title = None
            
            # 方法1: 查找h1标签
            title_element = soup.find('h1')
            if title_element:
                title = title_element.get_text(strip=True)
            
            # 方法2: 查找title标签
            if not title:
                title_element = soup.find('title')
                if title_element:
                    title = title_element.get_text(strip=True)
                    # 清理title标签中的网站名称
                    title = re.sub(r'\s*-\s*hanime1\.me.*$', '', title, flags=re.IGNORECASE)
            
            # 方法3: 查找特定class的元素
            if not title:
                title_candidates = soup.find_all(['h1', 'h2', 'h3'], class_=lambda x: x and 'title' in x.lower())
                if title_candidates:
                    title = title_candidates[0].get_text(strip=True)
            
            if title:
                self.logger.debug(f"找到标题: {title}")
                return title
            else:
                self.logger.warning(f"未找到标题: {video_url}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取标题失败 {video_url}: {e}")
            return None
        finally:
            driver.quit()
    
    def clean_title(self, title):
        """清理标题文本"""
        if not title:
            return None
        
        # 应用重命名规则
        rules = self.rename_config.get('rules', {})
        
        # 去掉方括号内容
        if rules.get('remove_brackets', True):
            # 去掉所有方括号及其内容: [中文字幕], [无码], [1080p] 等
            title = re.sub(r'\[([^\]]*)\]', '', title)
        
        # 去掉圆括号内容（可选）
        if rules.get('remove_parentheses', False):
            title = re.sub(r'\(([^\)]*)\)', '', title)
        
        # 去掉网站相关后缀和前缀
        if rules.get('remove_website_suffixes', True):
            # 从配置文件读取清理模式
            cleanup_patterns = self.rename_config.get('cleanup_patterns', {})
            website_suffixes = cleanup_patterns.get('website_suffixes', [
                "- H動漫.*$",
                "- hanime1?\\.me.*$", 
                "- 裏番.*$",
                "- 線上看.*$",
                "- 在线观看.*$",
                "- 免费观看.*$",
                "- 高清.*$",
                "- 无码.*$",
                "- 中文字幕.*$"
            ])
            
            # 默认的额外清理模式 - 更强的清理
            default_patterns = [
                r'\s*-\s*H動漫.*$',                   # - H動漫 相关
                r'\s*-\s*動漫.*$',                    # - 動漫 相关
                r'\s*-\s*成人.*$',                    # - 成人 相关
                r'\s*-\s*18\+.*$',                    # - 18+ 相关
                r'\s*-\s*NSFW.*$',                    # - NSFW 相关
                r'\s*-\s*裏番.*$',                    # - 裏番
                r'\s*-\s*線上看.*$',                  # - 線上看
                r'\s*/\s*裏番.*$',                    # /裏番
                r'\s*/\s*線上看.*$',                  # /線上看  
                r'【[^】]*】',                        # 【】符号内容
                r'《[^》]*》$',                       # 书名号（仅结尾）
                r'\s*第\d+集$',                      # 第X集（结尾）
                r'\s*EP\d+$',                        # EPX（结尾）
                r'\s*episode\s*\d+$',                # episode X（结尾）
                r'\s*／\s*裏番.*$',                   # ／裏番
                r'\s*／\s*線上看.*$',                 # ／線上看
            ]
            
            # 合并所有模式
            all_patterns = website_suffixes + default_patterns
            
            for pattern in all_patterns:
                title = re.sub(pattern, '', title, flags=re.IGNORECASE)
        
        # 去掉多余空格和标点符号
        if rules.get('trim_spaces', True):
            title = re.sub(r'\s+', ' ', title).strip()
            title = re.sub(r'^[-\s]+|[-\s]+$', '', title)  # 去掉首尾的横线和空格
            title = title.strip('- \t\n\r')
        
        # 替换非法字符
        if rules.get('replace_illegal_chars', True):
            for old_char, new_char in self.char_replacements.items():
                title = title.replace(old_char, new_char)
        
        # 去掉常见的无意义后缀
        cleanup_patterns = self.rename_config.get('cleanup_patterns', {})
        meaningless_suffixes = cleanup_patterns.get('meaningless_suffixes', [
            "\\s*完整版$",
            "\\s*全集$", 
            "\\s*合集$",
            "\\s*系列$",
            "\\s*特别版$",
            "\\s*导演剪辑版$",
            "\\s*未删减版$",
        ])
        
        for suffix in meaningless_suffixes:
            title = re.sub(suffix, '', title, flags=re.IGNORECASE)
        
        # 最终清理
        title = title.strip('- \t\n\r')
        
        # 长度限制
        max_length = self.rename_config.get('max_filename_length', 200)
        if len(title) > max_length:
            title = title[:max_length].strip()
        
        # 确保不为空
        if not title or len(title.strip()) == 0:
            return None
        
        return title
    
    def get_new_filename(self, original_filename, title):
        """根据标题生成新文件名"""
        if not title:
            return original_filename
        
        # 获取文件扩展名
        file_ext = Path(original_filename).suffix
        
        # 清理标题
        clean_title = self.clean_title(title)
        if not clean_title:
            return original_filename
        
        # 使用配置的模板
        template = self.rename_config.get('current_template', 'default')
        templates = self.rename_config.get('templates', {})
        
        if template in templates:
            # 这里可以扩展模板功能，目前主要使用标题
            new_name = clean_title
        else:
            new_name = clean_title
        
        # 添加扩展名
        new_filename = f"{new_name}{file_ext}"
        
        # 确保文件名长度合理
        max_length = self.rename_config.get('max_filename_length', 200)
        if len(new_filename) > max_length:
            # 截断标题部分，保留扩展名
            title_max_length = max_length - len(file_ext)
            if title_max_length > 0:
                new_name = new_name[:title_max_length].strip()
                new_filename = f"{new_name}{file_ext}"
        
        return new_filename
    
    def rename_file(self, file_path, offline_mode=False):
        """重命名单个文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                self.logger.warning(f"文件不存在: {file_path}")
                return False

            original_filename = file_path.name
            self.logger.info(f"处理文件: {original_filename}")

            title = None
            
            if offline_mode:
                # 离线模式：只做文件名清理，不联网获取标题
                self.logger.info(f"离线模式：直接清理文件名")
                # 使用原文件名作为标题进行清理
                title_without_ext = Path(original_filename).stem
                title = self.clean_title(title_without_ext)
                if not title:
                    title = title_without_ext
            else:
                # 在线模式：从网站获取标题
                # 从文件名提取视频ID
                video_id = self.extract_video_id_from_filename(original_filename)
                if not video_id:
                    self.logger.warning(f"无法从文件名提取视频ID，尝试离线清理: {original_filename}")
                    # 如果无法提取ID，自动切换到离线模式
                    title_without_ext = Path(original_filename).stem
                    title = self.clean_title(title_without_ext)
                    if not title:
                        title = title_without_ext
                else:
                    # 构建hanime1.me URL
                    video_url = self.build_hanime_url(video_id)

                    # 获取标题
                    title = self.get_title_from_hanime(video_url)
                    if not title:
                        self.logger.warning(f"无法获取标题，改用离线清理: {original_filename}")
                        # 如果无法获取标题，自动切换到离线模式
                        title_without_ext = Path(original_filename).stem
                        title = self.clean_title(title_without_ext)
                        if not title:
                            title = title_without_ext

            # 生成新文件名
            new_filename = self.get_new_filename(original_filename, title)
            if new_filename == original_filename:
                self.logger.info(f"文件名无需更改: {original_filename}")
                return True
            
            # 检查重复文件处理
            new_file_path = file_path.parent / new_filename
            if new_file_path.exists():
                duplicate_handling = self.rename_config.get('duplicate_handling', 'skip')
                
                if duplicate_handling == 'skip':
                    self.logger.info(f"目标文件已存在，跳过: {new_filename}")
                    return False
                elif duplicate_handling == 'rename':
                    # 添加数字后缀
                    base_name = new_file_path.stem
                    extension = new_file_path.suffix
                    counter = 1
                    while new_file_path.exists():
                        new_filename = f"{base_name}_{counter}{extension}"
                        new_file_path = file_path.parent / new_filename
                        counter += 1
                    self.logger.info(f"重复文件重命名为: {new_filename}")
                elif duplicate_handling == 'overwrite':
                    self.logger.info(f"覆盖已存在文件: {new_filename}")
            
            # 执行重命名
            file_path.rename(new_file_path)
            self.logger.info(f"✓ 重命名成功: {original_filename} -> {new_filename}")
            
            # 添加延迟避免请求过频繁
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"重命名文件失败 {file_path}: {e}")
            return False
    
    def find_video_files(self, directory):
        """查找目录中的视频文件"""
        video_extensions = self.download_config.get('file_formats', {}).get('video', ['.mp4', '.mkv', '.avi', '.mov', '.wmv'])
        
        video_files = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            self.logger.warning(f"目录不存在: {directory}")
            return video_files
        
        for ext in video_extensions:
            video_files.extend(directory_path.glob(f"*{ext}"))
            video_files.extend(directory_path.glob(f"**/*{ext}"))  # 递归查找
        
        return video_files
    
    def rename_directory(self, directory, offline_mode=False):
        """重命名目录中的所有视频文件"""
        self.logger.info(f"开始重命名目录: {directory}")
        
        video_files = self.find_video_files(directory)
        if not video_files:
            self.logger.info(f"目录中没有找到视频文件: {directory}")
            return True
        
        self.logger.info(f"找到 {len(video_files)} 个视频文件")
        
        success_count = 0
        for video_file in video_files:
            if self.rename_file(video_file, offline_mode=offline_mode):
                success_count += 1
        
        self.logger.info(f"重命名完成: 成功 {success_count}/{len(video_files)}")
        return success_count > 0
    
    def rename_all(self, offline_mode=False):
        """重命名所有下载目录中的文件"""
        self.logger.info("开始重命名所有文件")
        
        download_dir = self.download_config.get('download_dir', './downloads')
        
        if self.download_config.get('organize_by_date', True):
            # 按年月组织的目录结构
            base_path = Path(download_dir)
            if not base_path.exists():
                self.logger.warning(f"下载目录不存在: {download_dir}")
                return False
            
            # 查找所有年份目录
            year_dirs = [d for d in base_path.iterdir() if d.is_dir() and d.name.isdigit()]
            
            total_success = True
            for year_dir in year_dirs:
                # 查找月份目录
                month_dirs = [d for d in year_dir.iterdir() if d.is_dir()]
                
                for month_dir in month_dirs:
                    self.logger.info(f"处理目录: {month_dir}")
                    if not self.rename_directory(month_dir, offline_mode=offline_mode):
                        total_success = False
        else:
            # 平铺的目录结构
            total_success = self.rename_directory(download_dir, offline_mode=offline_mode)
        
        if total_success:
            self.logger.info("✓ 所有文件重命名完成")
        else:
            self.logger.warning("⚠ 部分文件重命名失败")
        
        return total_success

# 保持向后兼容
if __name__ == "__main__":
    # 简单的测试代码
    import yaml
    import logging
    
    # 加载配置
    with open('config.yml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建重命名器并运行
    renamer = HanimeRenamer(config, logger)
    renamer.rename_all()

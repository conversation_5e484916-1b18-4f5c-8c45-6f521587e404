#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动视频列表处理脚本
用于处理手动收集的视频链接
"""

import os
import re
from pathlib import Path
from typing import List
import logging
import sys
import yaml

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from craw import VideoInfo
from main import VideoProcessor


def read_video_list(file_path: str = "video_list.txt") -> List[str]:
    """读取视频链接列表"""
    video_urls = []
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        print("请创建video_list.txt文件，每行一个视频链接")
        print("示例格式:")
        print("https://hanime1.me/watch?v=12345")
        print("https://hanime1.me/watch?v=67890")
        return video_urls
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and line.startswith('http'):
                    video_urls.append(line)
        
        print(f"✅ 读取到 {len(video_urls)} 个视频链接")
        return video_urls
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return video_urls


def extract_video_id_from_url(url: str) -> str:
    """从URL中提取视频ID"""
    try:
        if '/watch?v=' in url:
            return url.split('/watch?v=')[1].split('&')[0]
        elif '/watch/' in url:
            return url.split('/watch/')[1].split('/')[0]
        else:
            # 尝试从URL末尾提取数字
            match = re.search(r'(\d+)', url.split('/')[-1])
            if match:
                return match.group(1)
            return url.split('/')[-1]
    except:
        return ""


def create_video_info_from_url(url: str) -> VideoInfo:
    """从URL创建VideoInfo对象"""
    video_id = extract_video_id_from_url(url)
    title = f"Video {video_id}" if video_id else "Unknown Video"
    
    return VideoInfo(
        id=video_id,
        title=title,
        url=url
    )


def process_video_list(video_urls: List[str], 
                      download: bool = True,
                      rename: bool = True,
                      scrape: bool = True) -> None:
    """处理视频列表"""
    if not video_urls:
        print("❌ 没有视频链接需要处理")
        return
    
    print(f"开始处理 {len(video_urls)} 个视频...")
    
    # 创建VideoInfo对象列表
    videos = []
    for url in video_urls:
        video_info = create_video_info_from_url(url)
        videos.append(video_info)
        print(f"  - {video_info.title} ({video_info.id})")
    
    # 创建处理器
    processor = VideoProcessor()
    
    try:
        # 获取详细信息
        print("\n步骤 1: 获取视频详细信息...")
        detailed_videos = []
        for i, video in enumerate(videos, 1):
            print(f"获取详情 ({i}/{len(videos)}): {video.title}")
            try:
                detailed_video = processor.crawler.get_video_details(video)
                detailed_videos.append(detailed_video)
            except Exception as e:
                print(f"  ⚠️ 获取详情失败: {e}")
                detailed_videos.append(video)  # 使用原始信息
        
        # 下载视频
        if download:
            print("\n步骤 2: 下载视频...")
            download_stats = processor.downloader.download_videos_batch(detailed_videos)
            
            if download_stats['completed'] == 0:
                print("⚠️ 没有成功下载的视频，跳过后续步骤")
                return
        
        # 重命名文件
        if rename:
            print("\n步骤 3: 重命名文件...")
            for video in detailed_videos:
                try:
                    processor.renamer.rename_video_file(video)
                except Exception as e:
                    print(f"  ⚠️ 重命名失败: {video.title} - {e}")
        
        # 刮削元数据
        if scrape:
            print("\n步骤 4: 刮削元数据...")
            for video in detailed_videos:
                try:
                    processor.scraper.scrape_and_save_nfo(video)
                except Exception as e:
                    print(f"  ⚠️ 刮削失败: {video.title} - {e}")
        
        print("\n🎉 处理完成！")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")


def create_sample_list():
    """创建示例视频列表文件"""
    sample_content = """# 视频链接列表
# 每行一个链接，以#开头的行为注释

# 示例链接（请替换为实际链接）
# https://hanime1.me/watch?v=12345
# https://hanime1.me/watch?v=67890
# https://hanime1.me/watch?v=99999

# 您可以在这里添加实际的视频链接
"""
    
    file_path = "video_list.txt"
    if not Path(file_path).exists():
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        print(f"✅ 创建示例文件: {file_path}")
        print("请编辑此文件，添加实际的视频链接")
    else:
        print(f"⚠️ 文件已存在: {file_path}")


def demonstrate_rename_modes():
    """演示在线和离线重命名模式的区别"""
    print("重命名模式演示")
    print("=" * 60)
    
    sample_files = [
        "OVA イクイク サキュバス再教育 ＃2 [中文字幕] - H動漫／裏番／線上看.mp4",
        "ママ喝っ ～姉ママの言い訳～ [1080p] - H動漫／裏番／線上看.mp4", 
        "神聖昂燐ダクリュオン・ルナ 前編 ～堕聖母誕生～ [无码] - 線上看.mp4",
        "[中文字幕] 魔法闘姫リルスティア 第三話 - hanime1.me.mp4"
    ]
    
    try:
        from rename import HanimeRenamer
        import logging
        
        # 禁用日志
        logging.disable(logging.CRITICAL)
        
        # 加载配置
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 创建虚拟logger
        class DummyLogger:
            def debug(self, msg): pass
            def info(self, msg): pass
            def warning(self, msg): pass
            def error(self, msg): pass
        
        renamer = HanimeRenamer(config, DummyLogger())
        
        print("🔗 在线模式：")
        print("   - 尝试从hanime1.me获取真实标题") 
        print("   - 如果无法获取则自动切换到离线模式")
        print("   - 可能有SSL连接问题")
        print()
        
        print("📱 离线模式：")
        print("   - 直接清理现有文件名")
        print("   - 去掉方括号、网站后缀等")
        print("   - 不需要网络连接，无SSL问题")
        print("   - 建议使用此模式！")
        print()
        
        print("示例效果（离线模式）：")
        print("-" * 60)
        
        for filename in sample_files:
            path = Path(filename)
            title = path.stem
            ext = path.suffix
            
            cleaned = renamer.clean_title(title)
            if cleaned:
                new_filename = f"{cleaned}{ext}"
                print(f"原文件名:")
                print(f"  {filename}")
                print(f"清理后:")
                print(f"  {new_filename}")
                print()
            else:
                print(f"❌ 清理失败: {filename}")
                print()
        
        # 恢复日志
        logging.disable(logging.NOTSET)
        
    except Exception as e:
        print(f"演示失败: {e}")
        logging.disable(logging.NOTSET)


def demonstrate_scraping():
    """演示增强版刮削功能"""
    print("增强版刮削功能演示")
    print("=" * 60)
    
    print("📚 支持的数据源：")
    print("  🔗 hanime1.me  - 获取标题、tag、摘要、年份")
    print("  🎬 themoviedb   - 获取中文摘要、评分、类型")
    print("  🖼️  getchu.com  - 获取剧照图片、制作商信息")
    print()
    
    print("🏷️  可获取的信息：")
    info_fields = [
        "title (标题)", "title_cn (中文标题)", "title_jp (日文标题)",
        "plot (剧情介绍)", "tag (标签)", "genre (类型)",
        "year (年份)", "studio (制作商)", "rating (评分)",
        "poster (海报)", "fanart (剧照)", "num (编号)"
    ]
    
    for i, field in enumerate(info_fields, 1):
        print(f"  {i:2d}. {field}")
    print()
    
    print("📄 NFO文件结构：")
    print("  ✅ 完整的XML结构（与示例NFO完全匹配）")
    print("  ✅ fileinfo 包含视频/音频/字幕信息")
    print("  ✅ set 系列信息自动提取")
    print("  ✅ 多语言标题支持")
    print("  ✅ CDATA 格式的剧情描述")
    print()
    
    # 示例文件名测试
    test_files = [
        "101447-1080p.mp4",
        "Video 99694_99694.mp4",
        "[GOLD BEAR] 神聖昂燐ダクリュオン・ルナ 前編 [GBR-033].mp4"
    ]
    
    print("🔍 视频ID提取测试：")
    print("-" * 40)
    
    try:
        from scape import HanimeScraper
        import logging
        
        # 禁用日志
        logging.disable(logging.CRITICAL)
        
        # 加载配置
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 创建虚拟logger
        class DummyLogger:
            def debug(self, msg): pass
            def info(self, msg): pass
            def warning(self, msg): pass
            def error(self, msg): pass
        
        scraper = HanimeScraper(config, DummyLogger())
        
        for filename in test_files:
            video_id = scraper.extract_video_id_from_filename(filename)
            if video_id:
                print(f"✅ {filename}")
                print(f"   → 视频ID: {video_id}")
                print(f"   → 将搜索: https://hanime1.me/watch?v={video_id}")
            else:
                print(f"❌ {filename}")
                print(f"   → 无法提取视频ID")
            print()
        
        # 恢复日志
        logging.disable(logging.NOTSET)
        
    except Exception as e:
        print(f"测试失败: {e}")
        logging.disable(logging.NOTSET)
    
    print("💡 使用建议：")
    print("  1. 运行 python quick_start.py 选择 '5. 仅刮削生成NFO'")
    print("  2. 确保配置文件中启用了所需的数据源")
    print("  3. 首次运行会下载图片到 downloads/images/ 目录")
    print("  4. 生成的NFO文件可直接用于媒体服务器")


def main():
    """主函数"""
    import argparse
    
    # 如果没有命令行参数，显示交互菜单
    if len(sys.argv) == 1:
        print("文件处理工具")
        print("=" * 30)
        print("1. 演示重命名模式")
        print("2. 演示刮削功能")
        print("3. 处理视频列表")
        print("4. 创建示例列表文件")
        print("0. 退出")
        
        choice = input("\n请选择功能: ").strip()
        
        if choice == '1':
            demonstrate_rename_modes()
        elif choice == '2':
            demonstrate_scraping()
        elif choice == '3':
            # 使用默认参数处理视频列表
            video_urls = read_video_list('video_list.txt')
            if not video_urls:
                print("没有找到视频链接，请先创建视频列表文件")
                return
            process_video_list(video_urls)
        elif choice == '4':
            create_sample_list()
        elif choice == '0':
            print("再见！")
        else:
            print("无效选择")
        return
    
    # 处理命令行参数
    parser = argparse.ArgumentParser(description='手动视频列表处理工具')
    parser.add_argument('--file', '-f', default='video_list.txt', help='视频列表文件路径')
    parser.add_argument('--no-download', action='store_true', help='不下载视频')
    parser.add_argument('--no-rename', action='store_true', help='不重命名文件')
    parser.add_argument('--no-scrape', action='store_true', help='不刮削元数据')
    parser.add_argument('--create-sample', action='store_true', help='创建示例列表文件')
    parser.add_argument('--list-only', action='store_true', help='只列出视频信息')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("手动视频列表处理工具")
    print("=" * 60)
    
    if args.create_sample:
        create_sample_list()
        return
    
    # 读取视频列表
    video_urls = read_video_list(args.file)
    
    if not video_urls:
        print("\n💡 提示:")
        print("1. 手动访问 https://hanime1.me")
        print("2. 找到您想要的视频")
        print("3. 复制视频链接到 video_list.txt 文件")
        print("4. 每行一个链接")
        print("5. 再次运行此脚本")
        print("\n或者运行: python process_manual_list.py --create-sample")
        return
    
    if args.list_only:
        # 只列出视频信息
        print("\n视频列表:")
        print("-" * 40)
        for i, url in enumerate(video_urls, 1):
            video_info = create_video_info_from_url(url)
            print(f"{i}. {video_info.title}")
            print(f"   ID: {video_info.id}")
            print(f"   URL: {video_info.url}")
            print()
    else:
        # 处理视频
        process_video_list(
            video_urls,
            download=not args.no_download,
            rename=not args.no_rename,
            scrape=not args.no_scrape
        )


if __name__ == '__main__':
    main()

# Hanime1.me 视频抓取和处理系统配置文件

# 网站抓取配置
crawl:
  # 基础URL配置
  base_url: "https://hanime1.me"
  search_url: "https://hanime1.me/search"

  # 搜索参数
  search_params:
    genre: "%E8%A3%8F%E7%95%AA" # 裏番 (URL编码)
    type: "" # 视频类型
    sort: "" # 排序方式
    query: "" # 搜索关键词

  # 时间筛选（从配置读取）
  date_filter:
    enabled: true
    year: 2025 # 年份，0表示不限制
    month: 1 # 月份，0表示不限制
    day: 0 # 日期，0表示不限制

  # 抓取设置
  settings:
    max_pages: 10 # 最大抓取页数
    delay_between_requests: 5 # 请求间隔（秒）
    timeout: 30 # 请求超时时间
    retry_times: 5 # 重试次数
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

  # 代理设置（可选）
  proxy:
    enabled: false
    http: ""
    https: ""

# 下载配置
download:
  # 下载目录 - 按年月组织
  download_dir: "./downloads"
  temp_dir: "./temp"

  # 目录结构：downloads/年份/月份/
  organize_by_date: true

  # 下载设置
  settings:
    max_concurrent: 3 # 最大并发下载数
    chunk_size: 1024 # 下载块大小（KB）
    timeout: 300 # 下载超时时间（秒）
    retry_times: 3 # 重试次数
    resume_download: true # 支持断点续传

    # 视频质量优先级：1080p > 720p > 480p
    quality_priority: ["1080", "720", "480"]

  # 文件格式过滤
  file_formats:
    video: [".mp4", ".mkv", ".avi", ".mov", ".wmv"]
    subtitle: [".srt", ".ass", ".vtt"]
    image: [".jpg", ".jpeg", ".png", ".webp"]

# 重命名配置
rename:
  # 重命名规则
  rules:
    # 去掉方括号内容
    remove_brackets: true
    # 去掉圆括号内容（可选）
    remove_parentheses: false
    # 去掉多余空格
    trim_spaces: true
    # 替换非法字符
    replace_illegal_chars: true
    # 去掉网站相关后缀
    remove_website_suffixes: true

  # 自定义清理模式（高级用户可修改）
  cleanup_patterns:
    # 网站相关后缀
    website_suffixes:
      - "- H動漫.*$"
      - "- hanime1?\\.me.*$"
      - "- 裏番.*$"
      - "- 線上看.*$"
      - "- 在线观看.*$"
      - "- 免费观看.*$"
      - "- 高清.*$"
      - "- 无码.*$"
      - "- 中文字幕.*$"

    # 无意义后缀
    meaningless_suffixes:
      - "\\s*完整版$"
      - "\\s*全集$"
      - "\\s*合集$"
      - "\\s*系列$"
      - "\\s*特别版$"
      - "\\s*导演剪辑版$"
      - "\\s*未删减版$"

  # 命名规则模板
  templates:
    # 默认模板：标题
    default: "{title}"
    # 带制作商模板：[制作商] 标题
    with_studio: "[{studio}] {title}"
    # 带编号模板：标题 [编号]
    with_num: "{title} [{num}]"

  # 当前使用的模板
  current_template: "default"

  # 字符替换规则
  char_replacements:
    "/": "／"
    "\\": "＼"
    ":": "："
    "*": "＊"
    "?": "？"
    '"': "＂"
    "<": "＜"
    ">": "＞"
    "|": "｜"

  # 长度限制
  max_filename_length: 200

  # 重复文件处理
  duplicate_handling: "skip" # skip, overwrite, rename

# 刮削配置
scraping:
  # 数据源配置
  sources:
    hanime1:
      enabled: true
      base_url: "https://hanime1.me"
      priority: 1

    themoviedb:
      enabled: false
      base_url: "https://api.themoviedb.org/3"
      api_key: "afa7ddee5daea7c80f4bc5e4dff17b24" # 需要申请API密钥
      priority: 2

    getchu:
      enabled: true
      base_url: "https://www.getchu.com"
      priority: 3

  # NFO文件配置
  nfo:
    encoding: "utf-8"
    template_file: "" # 可选的NFO模板文件路径

    # 默认字段映射
    field_mapping:
      title: "title"
      originaltitle: "originaltitle"
      plot: "plot"
      outline: "outline"
      year: "year"
      premiered: "premiered"
      releasedate: "releasedate"
      runtime: "runtime"
      rating: "rating"
      studio: "studio"
      maker: "maker"
      num: "num"
      genre: "genre"
      tag: "tag"
      customrating: "customrating"
      mpaa: "mpaa"

  # 图片下载配置
  images:
    download_poster: true
    download_fanart: true
    download_thumb: true
    poster_size: "original" # original, large, medium, small
    fanart_size: "original"

  # 刮削设置
  settings:
    delay_between_requests: 1 # 请求间隔（秒）
    timeout: 30 # 请求超时时间
    retry_times: 3 # 重试次数
    skip_existing_nfo: true # 跳过已存在的NFO文件

# 日志配置
logging:
  level: "INFO" # DEBUG, INFO, WARNING, ERROR
  file: "scraper.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 通知配置（可选）
notifications:
  enabled: false

# 数据库配置（可选，用于记录处理历史）
database:
  enabled: false
  type: "sqlite" # sqlite, mysql, postgresql
  path: "scraper.db" # SQLite数据库文件路径

# 高级设置
advanced:
  # 并发控制
  max_workers: 5

  # 内存限制
  max_memory_usage: "1GB"

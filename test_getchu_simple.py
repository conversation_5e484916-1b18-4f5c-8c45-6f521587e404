import requests
from lxml import html
import urllib.parse

# 搜索词
url = '彼女フェイス THE ANIMATION 第1巻'

# URL编码
encoded_url = urllib.parse.quote_plus(url, encoding='cp932')

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
}

# 构建搜索URL
getchu_url = f"https://www.getchu.com/php/search.phtml?genre=all&search_keyword={encoded_url}&gc=gc"
print(f"搜索词: {url}")
print(f"搜索URL: {getchu_url}")

try:
    response = requests.get(getchu_url, headers=headers, timeout=30)
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        tree = html.fromstring(response.content)
        
        # 直接搜索所有包含soft.phtml?id=的链接
        all_soft_links = tree.xpath('//a[contains(@href, "soft.phtml?id=")]/@href')
        print(f"找到 {len(all_soft_links)} 个soft.phtml链接")
        
        if all_soft_links:
            # 取第一个链接
            href = all_soft_links[0]
            print(f"找到的链接: {href}")
            
            # 处理链接
            if href.startswith('../'):
                href = href[3:]  # 移除 '../'
            
            if not href.startswith('http'):
                final_url = 'https://www.getchu.com/' + href
            else:
                final_url = href
                
            print(f"最终URL: {final_url}")
        else:
            print("没有找到任何soft.phtml链接")
            
            # 尝试搜索包含标题的链接
            title_links = tree.xpath(f'//a[contains(text(), "{url}")]/@href')
            print(f"包含标题的链接: {len(title_links)} 个")
            if title_links:
                print("标题链接:", title_links[0])
    else:
        print(f"请求失败，状态码: {response.status_code}")
        
except Exception as e:
    print(f"发生错误: {e}")

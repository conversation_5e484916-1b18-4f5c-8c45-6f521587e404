import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import time
from lxml import html
import re
import urllib.parse

# 可以修改这个搜索词
url = 'ママ喝っ ～姉ママの言い訳～'

#url编码
encoded_url = urllib.parse.quote_plus(url, encoding='cp932')

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
}

# 构建搜索URL
getchu_url = f"https://www.getchu.com/php/search.phtml?genre=all&search_keyword={encoded_url}&gc=gc"
print(f"搜索词: {url}")
print(f"搜索URL: {getchu_url}")

response = requests.get(getchu_url, headers=headers, timeout=30)

# 检查响应状态
print(f"响应状态码: {response.status_code}")
print(f"响应内容长度: {len(response.content)} 字节")

if response.status_code != 200:
    print(f"请求失败，状态码: {response.status_code}")
    exit(1)

# 保存HTML内容用于调试
with open('debug_getchu.html', 'w', encoding='utf-8') as f:
    f.write(response.text)
print("HTML内容已保存到 debug_getchu.html")

tree = html.fromstring(response.content)

# 尝试多种XPath策略
xpath_strategies = [
    # 策略1：直接搜索包含完整标题的链接
    '//a[contains(text(), "{}")]/@href'.format(url),
    # 策略2：搜索包含部分标题的链接
    '//a[contains(text(), "{}")]/@href'.format(url.split()[0]),  # 使用第一个词
    # 策略3：搜索所有soft.phtml链接，然后手动筛选
    '//a[contains(@href, "soft.phtml?id=")]/@href',
    # 策略4：搜索列表项中的链接
    '//li//a[contains(@href, "soft.phtml")]/@href',
    # 策略5：原始复杂策略
    '//li[.//a[contains(normalize-space(translate(translate(., "\u3000", " "), "\u00A0", " ")), "{}")]]//a[1]/@href'.format(url)
]

href = None
for i, xpath_expr in enumerate(xpath_strategies):
    try:
        print(f"尝试XPath策略 {i+1}: {xpath_expr[:50]}...")
        results = tree.xpath(xpath_expr)
        print(f"策略 {i+1} 找到 {len(results)} 个结果")

        if results:
            href = results[0]
            print(f"使用策略 {i+1} 找到链接: {href}")
            break
    except Exception as e:
        print(f"策略 {i+1} 出错: {e}")
        continue

if href is None:
    print("所有XPath策略都失败了，尝试查看页面内容...")
    # 输出页面的一些基本信息
    all_links = tree.xpath('//a/@href')
    print(f"页面总共有 {len(all_links)} 个链接")

    # 查找包含soft.phtml的链接
    soft_links = [link for link in all_links if 'soft.phtml' in str(link)]
    print(f"包含 'soft.phtml' 的链接有 {len(soft_links)} 个")

    if soft_links:
        print("前5个soft.phtml链接:")
        for link in soft_links[:5]:
            print(f"  {link}")

    print("请检查 debug_getchu.html 文件来分析页面结构")
    exit(1)

# 处理链接
if 'soft.phtml?id=' in href:
    # 如果已经是完整链接
    if href.startswith('http'):
        final_url = href
    else:
        final_url = 'https://www.getchu.com' + href
else:
    # 如果需要提取ID
    href = href.split('=')[-1]
    final_url = 'https://www.getchu.com/soft.phtml?id=' + href

print(f"最终URL: {final_url}")



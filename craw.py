#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频抓取下载模块
"""

import os
import re
import time
import requests
from bs4 import BeautifulSoup
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException
from urllib.parse import urljoin, urlparse
from pathlib import Path

class HanimeCrawler:
    """Hanime1.me 爬虫类"""
    
    def __init__(self, config, logger):
        """初始化爬虫"""
        self.config = config
        self.logger = logger
        self.scraper = cloudscraper.create_scraper()
        
        # 从配置获取参数
        self.crawl_config = config.get('crawl', {})
        self.download_config = config.get('download', {})
        
        # 设置基础URL
        self.base_url = self.crawl_config.get('base_url', 'https://hanime1.me')
        self.search_url = self.crawl_config.get('search_url', 'https://hanime1.me/search')
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.crawl_config.get('settings', {}).get('user_agent', 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        }
        
        # 设置代理
        proxy_config = self.crawl_config.get('proxy', {})
        if proxy_config.get('enabled'):
            self.proxies = {
                'http': proxy_config.get('http'),
                'https': proxy_config.get('https')
            }
        else:
            self.proxies = None
    
    def build_search_url(self, year=None, month=None):
        """构建搜索URL"""
        search_params = self.crawl_config.get('search_params', {})
        date_filter = self.crawl_config.get('date_filter', {})
        
        # 使用传入的年月或配置中的年月
        if year is None:
            year = date_filter.get('year', 2025)
        if month is None:
            month = date_filter.get('month', 1)
        
        # 构建URL参数
        params = {
            'query': search_params.get('query', ''),
            'type': search_params.get('type', ''),
            'genre': search_params.get('genre', '%E8%A3%8F%E7%95%AA'),
            'sort': search_params.get('sort', '')
        }
        
        # 添加年月参数
        if year and year > 0:
            params['year'] = str(year)
        if month and month > 0:
            params['month'] = str(month)
        
        # 构建完整URL
        param_str = '&'.join([f"{k}={v}" for k, v in params.items() if v])
        url = f"{self.search_url}?{param_str}"
        
        self.logger.info(f"搜索URL: {url}")
        return url
    
    def get_video_links(self, search_url):
        """从搜索页面获取视频链接"""
        try:
            self.logger.info(f"获取搜索页面: {search_url}")
            response = self.scraper.get(search_url, headers=self.headers, proxies=self.proxies)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 匹配视频链接
            pattern = r'"(https://hanime1\.me/watch\?[^\s"]+)"'
            matches = re.findall(pattern, str(soup))
            
            # 去重
            video_links = list(set(matches))
            self.logger.info(f"找到 {len(video_links)} 个视频链接")
            
            return video_links
            
        except requests.RequestException as e:
            self.logger.error(f"获取搜索页面失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"解析搜索页面失败: {e}")
            return []
    
    def setup_webdriver(self):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        
        # SSL 相关设置，解决握手失败问题
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        chrome_options.add_argument('--ignore-ssl-errors-accept-any-certs')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-javascript')
        
        chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')
        
        # 设置代理
        if self.proxies and self.proxies.get('http'):
            proxy = self.proxies['http']
            chrome_options.add_argument(f'--proxy-server={proxy}')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            # 设置超时
            driver.set_page_load_timeout(30)
            return driver
        except WebDriverException as e:
            self.logger.error(f"Chrome WebDriver 初始化失败: {e}")
            return None
    
    def get_video_info(self, video_url):
        """获取视频详细信息"""
        self.logger.info(f"获取视频信息: {video_url}")
        
        driver = self.setup_webdriver()
        if not driver:
            return None
        
        try:
            driver.get(video_url)
            driver.implicitly_wait(5)
            page_content = driver.page_source
            
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # 获取视频标题
            title_element = soup.find('h1')
            title = title_element.get_text(strip=True) if title_element else "Unknown"
            
            # 获取视频下载链接 - 按质量优先级
            quality_priority = self.download_config.get('settings', {}).get('quality_priority', ["1080", "720", "480"])
            video_src = None
            quality_found = None
            
            for quality in quality_priority:
                source = soup.find('source', {'size': quality})
                if source and source.get('src'):
                    video_src = source.get('src')
                    quality_found = quality
                    self.logger.info(f"找到 {quality}p 视频链接")
                    break
            
            if not video_src:
                # 尝试获取备用链接
                input_element = soup.find('input', {'id': 'video-sd'})
                if input_element and input_element.get('value'):
                    backup_url = input_element['value'].split("?")[0]
                    filename = backup_url.split('/')[-1]
                    self.logger.info(f"使用备用链接: {filename}")
                else:
                    self.logger.warning(f"未找到视频下载链接: {video_url}")
                    return None
            
            return {
                'url': video_url,
                'title': title,
                'video_src': video_src,
                'quality': quality_found,
                'filename': self.extract_filename(video_src) if video_src else None
            }
            
        except Exception as e:
            self.logger.error(f"获取视频信息失败 {video_url}: {e}")
            return None
        finally:
            driver.quit()
    
    def extract_filename(self, video_url):
        """从视频URL提取文件名"""
        try:
            parsed = urlparse(video_url)
            filename = os.path.basename(parsed.path)
            if not filename or not any(filename.endswith(ext) for ext in ['.mp4', '.mkv', '.avi']):
                # 生成默认文件名
                video_id = re.search(r'(\d+)', video_url)
                if video_id:
                    filename = f"{video_id.group(1)}-1080p.mp4"
                else:
                    filename = "video.mp4"
            return filename
        except Exception:
            return "video.mp4"
    
    def download_video(self, video_info, download_path):
        """下载视频文件"""
        if not video_info or not video_info.get('video_src'):
            self.logger.error("视频信息无效，无法下载")
            return False
        
        try:
            video_url = video_info['video_src']
            filename = video_info['filename']
            save_path = os.path.join(download_path, filename)
            
            # 检查文件是否已存在
            if os.path.exists(save_path):
                if self.download_config.get('settings', {}).get('resume_download', True):
                    file_size = os.path.getsize(save_path)
                    headers = dict(self.headers)
                    headers['Range'] = f'bytes={file_size}-'
                    self.logger.info(f"断点续传: {filename} (已下载 {file_size} 字节)")
                elif self.config.get('rename', {}).get('duplicate_handling') == 'skip':
                    self.logger.info(f"文件已存在，跳过: {filename}")
                    return True
            
            # 创建临时文件
            temp_path = os.path.join(self.download_config.get('temp_dir', './temp'), f"{filename}.tmp")
            Path(temp_path).parent.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"开始下载: {filename}")
            
            with requests.get(video_url, stream=True, headers=self.headers, proxies=self.proxies) as response:
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(temp_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=1024*1024):  # 1MB chunks
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                self.logger.debug(f"下载进度: {progress:.1f}%")
            
            # 移动到最终位置
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            os.rename(temp_path, save_path)
            
            self.logger.info(f"✓ 下载完成: {save_path}")
            return True
            
        except requests.RequestException as e:
            self.logger.error(f"下载失败 {video_info.get('title', 'Unknown')}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"下载过程中出错 {video_info.get('title', 'Unknown')}: {e}")
            return False
    
    def crawl(self, year=None, month=None):
        """执行完整的爬取流程"""
        self.logger.info("开始爬取流程")
        
        # 从配置获取年月
        date_filter = self.crawl_config.get('date_filter', {})
        if year is None:
            year = date_filter.get('year', 2025)
        if month is None:
            month = date_filter.get('month', 1)
        
        self.logger.info(f"爬取参数: 年份={year}, 月份={month}")
        
        # 构建搜索URL
        search_url = self.build_search_url(year, month)
        
        # 获取视频链接
        video_links = self.get_video_links(search_url)
        if not video_links:
            self.logger.warning("未找到任何视频链接")
            return False
        
        # 创建下载目录
        download_path = self.get_download_path(year, month)
        Path(download_path).mkdir(parents=True, exist_ok=True)
        self.logger.info(f"下载目录: {download_path}")
        
        # 逐个处理视频
        success_count = 0
        total_count = len(video_links)
        
        for i, video_url in enumerate(video_links, 1):
            self.logger.info(f"处理视频 {i}/{total_count}: {video_url}")
            
            # 获取视频信息
            video_info = self.get_video_info(video_url)
            if not video_info:
                self.logger.warning(f"跳过无法获取信息的视频: {video_url}")
                continue
            
            # 下载视频
            if self.download_video(video_info, download_path):
                success_count += 1
            
            # 请求间隔
            delay = self.crawl_config.get('settings', {}).get('delay_between_requests', 5)
            if i < total_count:  # 最后一个不需要延迟
                self.logger.debug(f"等待 {delay} 秒...")
                time.sleep(delay)
        
        self.logger.info(f"爬取完成: 成功 {success_count}/{total_count}")
        return success_count > 0
    
    def get_download_path(self, year, month):
        """获取下载路径"""
        base_dir = self.download_config.get('download_dir', './downloads')
        
        if self.download_config.get('organize_by_date', True):
            return os.path.join(base_dir, str(year), f"{month:02d}")
        
        return base_dir

# 保持向后兼容
if __name__ == "__main__":
    # 简单的测试代码
    import yaml
    import logging
    
    # 加载配置
    with open('config.yml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建爬虫并运行
    crawler = HanimeCrawler(config, logger)
    crawler.crawl()


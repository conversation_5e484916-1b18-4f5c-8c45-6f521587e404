#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频抓取处理系统 - 快速启动脚本
提供简单的交互式界面
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Hanime1.me 视频处理系统                    ║
║                     Video Processing System                    ║
╠══════════════════════════════════════════════════════════════╣
║  功能: 视频抓取、下载、重命名、刮削                            ║
║  支持: hanime1.me / themoviedb.org / getchu.com              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """打印主菜单"""
    menu = """
┌─────────────────── 主菜单 ───────────────────┐
│  1. 完整流程 (抓取+重命名+刮削)                │
│  2. 仅抓取下载                                │
│  3. 在线重命名（联网获取真实标题）              │
│  4. 离线重命名（仅清理文件名）                  │
│  5. 刮削视频信息                              │
│  6. 修改配置                                  │
│  7. 查看下载目录                              │
│  8. 测试重命名效果                            │
│  0. 退出程序                                  │
└─────────────────────────────────────────────┘
    """
    print(menu)

def get_year_month():
    """获取用户输入的年月"""
    print("\n配置抓取参数:")
    
    # 获取年份
    while True:
        year_input = input("请输入年份 (直接回车使用配置文件中的值): ").strip()
        if not year_input:
            year = None
            break
        try:
            year = int(year_input)
            if 2000 <= year <= 2030:
                break
            else:
                print("年份应在2000-2030之间")
        except ValueError:
            print("请输入有效的年份")
    
    # 获取月份
    while True:
        month_input = input("请输入月份 (1-12, 直接回车使用配置文件中的值): ").strip()
        if not month_input:
            month = None
            break
        try:
            month = int(month_input)
            if 1 <= month <= 12:
                break
            else:
                print("月份应在1-12之间")
        except ValueError:
            print("请输入有效的月份")
    
    return year, month

def run_main_program(mode, year=None, month=None, offline_mode=False):
    """运行主程序"""
    try:
        from main import HanimeProcessor
        
        print(f"\n开始执行模式: {mode}")
        if year or month:
            print(f"参数: 年份={year or '配置文件'}, 月份={month or '配置文件'}")
        if offline_mode:
            if mode == 'rename':
                print("模式: 离线重命名（仅清理文件名，不联网获取标题）")
            elif mode == 'scrape':
                print("模式: 离线刮削（基于文件名生成基础NFO）")
        
        processor = HanimeProcessor()
        
        # 如果指定了年月，更新配置
        if year:
            processor.config['crawl']['date_filter']['year'] = year
        if month:
            processor.config['crawl']['date_filter']['month'] = month
        
        # 运行指定模式
        if mode == 'all':
            success = processor.run_all()
        elif mode == 'crawl':
            success = processor.run_crawler()
        elif mode == 'rename':
            success = processor.run_renamer(offline_mode=offline_mode)
        elif mode == 'scrape':
            success = processor.scrape(offline_mode=offline_mode)
        else:
            print(f"未知模式: {mode}")
            return False
        
        if success:
            print(f"\n✓ 模式 '{mode}' 执行完成")
        else:
            print(f"\n✗ 模式 '{mode}' 执行失败")
        
        return success
        
    except Exception as e:
        print(f"\n✗ 执行失败: {e}")
        return False

def show_config():
    """显示和修改配置"""
    try:
        import yaml
        
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("\n当前配置:")
        print("─" * 50)
        
        # 显示主要配置项
        crawl_config = config.get('crawl', {})
        date_filter = crawl_config.get('date_filter', {})
        
        print(f"抓取年份: {date_filter.get('year', '未设置')}")
        print(f"抓取月份: {date_filter.get('month', '未设置')}")
        print(f"下载目录: {config.get('download', {}).get('download_dir', './downloads')}")
        print(f"临时目录: {config.get('download', {}).get('temp_dir', './temp')}")
        
        rename_config = config.get('rename', {})
        print(f"去掉方括号: {rename_config.get('rules', {}).get('remove_brackets', True)}")
        print(f"重命名模板: {rename_config.get('current_template', 'default')}")
        
        scraping_config = config.get('scraping', {})
        sources = scraping_config.get('sources', {})
        print(f"hanime1.me: {'启用' if sources.get('hanime1', {}).get('enabled', True) else '禁用'}")
        print(f"themoviedb: {'启用' if sources.get('themoviedb', {}).get('enabled', True) else '禁用'}")
        print(f"getchu: {'启用' if sources.get('getchu', {}).get('enabled', True) else '禁用'}")
        
        print("\n如需修改配置，请直接编辑 config.yml 文件")
        
    except Exception as e:
        print(f"读取配置失败: {e}")

def show_downloads():
    """显示下载目录"""
    try:
        import yaml
        from pathlib import Path
        
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        download_dir = config.get('download', {}).get('download_dir', './downloads')
        download_path = Path(download_dir)
        
        print(f"\n下载目录: {download_path.absolute()}")
        print("─" * 50)
        
        if not download_path.exists():
            print("下载目录不存在")
            return
        
        # 显示目录结构
        for year_dir in sorted(download_path.iterdir()):
            if year_dir.is_dir() and year_dir.name.isdigit():
                print(f"📁 {year_dir.name}/")
                
                for month_dir in sorted(year_dir.iterdir()):
                    if month_dir.is_dir():
                        # 统计文件数量
                        video_files = list(month_dir.glob("*.mp4")) + list(month_dir.glob("*.mkv")) + list(month_dir.glob("*.avi"))
                        nfo_files = list(month_dir.glob("*.nfo"))
                        
                        print(f"  📁 {month_dir.name}/ (视频: {len(video_files)}, NFO: {len(nfo_files)})")
        
    except Exception as e:
        print(f"读取下载目录失败: {e}")

def test_rename():
    """测试重命名效果"""
    print("\n测试重命名功能")
    print("─" * 50)
    
    # 测试用例
    test_cases = [
        "OVA イクイク サキュバス再教育 ＃2 [中文字幕] - H動漫／裏番／線上看.mp4",
        "神聖昂燐ダクリュオン・ルナ 前編 ～堕聖母誕生～ [1080p] - hanime1.me.mp4",
        "【里番】某某動漫 第1集 [无码中文字幕] - 免费观看.mp4",
        "测试视频 (2024) [高清] - 線上看完整版.mkv"
    ]
    
    try:
        from rename import HanimeRenamer
        import yaml
        import logging
        
        # 临时禁用日志输出
        logging.disable(logging.CRITICAL)
        
        # 加载配置
        with open('config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 创建虚拟logger
        class DummyLogger:
            def debug(self, msg): pass
            def info(self, msg): pass
            def warning(self, msg): pass
            def error(self, msg): pass
        
        renamer = HanimeRenamer(config, DummyLogger())
        
        print("原始文件名 → 清理后文件名:")
        print("─" * 50)
        
        for test_case in test_cases:
            # 提取文件名（不含扩展名）
            from pathlib import Path
            path = Path(test_case)
            title = path.stem
            ext = path.suffix
            
            # 清理标题
            cleaned = renamer.clean_title(title)
            if cleaned:
                cleaned_filename = f"{cleaned}{ext}"
                print(f"✓ {test_case}")
                print(f"  → {cleaned_filename}")
            else:
                print(f"✗ {test_case}")
                print(f"  → [清理后为空]")
            print()
        
        # 恢复日志
        logging.disable(logging.NOTSET)
        
    except ImportError as e:
        print(f"无法导入重命名模块: {e}")
    except Exception as e:
        print(f"测试失败: {e}")
        # 恢复日志
        logging.disable(logging.NOTSET)

def main():
    """主函数"""
    print_banner()
    
    # 检查配置文件
    if not Path('config.yml').exists():
        print("✗ 配置文件 config.yml 不存在！")
        print("请确保配置文件存在后再运行程序。")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("请选择功能 (0-8): ").strip()
            
            if choice == '0':
                print("\n再见！")
                break
            elif choice == '1':
                # 完整流程
                year, month = get_year_month()
                run_main_program('all', year, month)
            elif choice == '2':
                # 仅抓取下载
                year, month = get_year_month()
                run_main_program('crawl', year, month)
            elif choice == '3':
                # 在线重命名（联网获取真实标题）
                print("\n开始重命名现有文件（在线模式）...")
                run_main_program('rename', offline_mode=False)
            elif choice == '4':
                # 离线重命名（仅清理文件名）
                print("\n开始重命名现有文件（离线模式）...")
                run_main_program('rename', offline_mode=True)
            elif choice == '5':
                # 刮削视频信息
                print("\n=== 视频信息刮削 ===")
                print("选择刮削模式：")
                print("1. 在线刮削（联网获取详细信息）")
                print("2. 离线刮削（基于文件名生成基础信息）")
                
                scrape_choice = input("请选择模式 (1-2): ").strip()
                
                if scrape_choice == '1':
                    print("\n开始在线刮削...")
                    run_main_program('scrape', offline_mode=False)
                elif scrape_choice == '2':
                    print("\n开始离线刮削...")
                    run_main_program('scrape', offline_mode=True)
                else:
                    print("无效选择")
            elif choice == '6':
                # 显示配置
                show_config()
            elif choice == '7':
                # 查看下载目录
                show_downloads()
            elif choice == '8':
                # 测试重命名效果
                test_rename()
            else:
                print("无效选择，请重新输入")
            
            if choice != '0':
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()

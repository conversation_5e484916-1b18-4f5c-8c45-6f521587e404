#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频抓取和处理系统
主程序入口
"""

import os
import sys
import yaml
import logging
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class HanimeProcessor:
    """Hanime1.me 视频处理器主类"""
    
    def __init__(self, config_path="config.yml"):
        """初始化处理器"""
        self.config_path = config_path
        self.config = None
        self.logger = None
        
        # 加载配置
        self.load_config()
        
        # 设置日志
        self.setup_logging()
        
        # 创建必要目录
        self.create_directories()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            print(f"✓ 配置文件加载成功: {self.config_path}")
        except FileNotFoundError:
            print(f"✗ 配置文件不存在: {self.config_path}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"✗ 配置文件格式错误: {e}")
            sys.exit(1)
    
    def setup_logging(self):
        """设置日志系统"""
        log_config = self.config.get('logging', {})
        
        # 创建logger
        self.logger = logging.getLogger('HanimeProcessor')
        self.logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # 创建formatter
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_config.get('file'):
            file_handler = logging.FileHandler(
                log_config['file'], 
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        self.logger.info("日志系统初始化完成")
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config['download']['download_dir'],
            self.config['download']['temp_dir']
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建目录: {directory}")
    
    def get_download_path(self, year=None, month=None):
        """获取下载路径"""
        base_dir = self.config['download']['download_dir']
        
        if self.config['download'].get('organize_by_date', True):
            if year and month:
                return os.path.join(base_dir, str(year), f"{month:02d}")
            else:
                # 使用配置中的年月
                config_year = self.config['crawl']['date_filter']['year']
                config_month = self.config['crawl']['date_filter']['month']
                return os.path.join(base_dir, str(config_year), f"{config_month:02d}")
        
        return base_dir
    
    def run_crawler(self):
        """运行爬虫"""
        self.logger.info("开始运行爬虫...")
        
        try:
            # 导入爬虫模块
            from craw import HanimeCrawler
            
            crawler = HanimeCrawler(self.config, self.logger)
            crawler.crawl()
            
        except ImportError as e:
            self.logger.error(f"无法导入爬虫模块: {e}")
            return False
        except Exception as e:
            self.logger.error(f"爬虫运行失败: {e}")
            return False
        
        return True
    
    def run_renamer(self, offline_mode=False):
        """运行重命名器"""
        if offline_mode:
            self.logger.info("开始运行重命名器（离线模式）...")
        else:
            self.logger.info("开始运行重命名器（在线模式）...")
        
        try:
            from rename import HanimeRenamer
            
            renamer = HanimeRenamer(self.config, self.logger)
            renamer.rename_all(offline_mode=offline_mode)
            
        except ImportError as e:
            self.logger.error(f"无法导入重命名模块: {e}")
            return False
        except Exception as e:
            self.logger.error(f"重命名失败: {e}")
            return False
        
        return True
    
    def scrape(self, directory=None, offline_mode=False):
        """刮削视频信息并生成NFO文件"""
        try:
            if not hasattr(self, 'scraper') or self.scraper is None:
                from scape import HanimeScraper
                self.scraper = HanimeScraper(self.config, self.logger)
            
            if directory:
                target_dir = Path(directory)
            else:
                downloads_dir = Path(self.config['download']['download_dir'])
                current_year = self.config['crawl']['date_filter'].get('year', datetime.now().year)
                current_month = self.config['crawl']['date_filter'].get('month', datetime.now().month)
                target_dir = downloads_dir / str(current_year) / f"{current_month:02d}"
            
            self.logger.info(f"开始刮削目录: {target_dir}")
            
            if offline_mode:
                self.logger.info("使用离线模式进行刮削")
            else:
                self.logger.info("使用在线模式进行刮削（支持标题搜索）")
            
            success_count = self.scraper.scrape_directory(target_dir, offline_mode=offline_mode)
            
            if success_count > 0:
                self.logger.info(f"✅ 刮削完成: 成功 {success_count} 个文件")
            else:
                self.logger.warning("⚠ 所有文件刮削失败")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"刮削过程出错: {e}")
            return False
    
    def run_all(self):
        """运行完整流程"""
        self.logger.info("=" * 50)
        self.logger.info("开始完整处理流程")
        self.logger.info("=" * 50)
        
        steps = [
            ("抓取下载", self.run_crawler),
            ("重命名", self.run_renamer),
            ("刮削", self.scrape)
        ]
        
        for step_name, step_func in steps:
            self.logger.info(f"开始执行步骤: {step_name}")
            if step_func():
                self.logger.info(f"✓ 步骤完成: {step_name}")
            else:
                self.logger.error(f"✗ 步骤失败: {step_name}")
                return False
        
        self.logger.info("=" * 50)
        self.logger.info("完整流程执行完成")
        self.logger.info("=" * 50)
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Hanime1.me 视频抓取和处理系统')
    parser.add_argument('-c', '--config', default='config.yml', help='配置文件路径')
    parser.add_argument('-m', '--mode', choices=['all', 'crawl', 'rename', 'scrape'], 
                        default='all', help='运行模式')
    parser.add_argument('-y', '--year', type=int, help='指定年份')
    parser.add_argument('-M', '--month', type=int, help='指定月份')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = HanimeProcessor(args.config)
    
    # 如果指定了年月，更新配置
    if args.year:
        processor.config['crawl']['date_filter']['year'] = args.year
    if args.month:
        processor.config['crawl']['date_filter']['month'] = args.month
    
    # 运行指定模式
    if args.mode == 'all':
        success = processor.run_all()
    elif args.mode == 'crawl':
        success = processor.run_crawler()
    elif args.mode == 'rename':
        success = processor.run_renamer()
    elif args.mode == 'scrape':
        success = processor.scrape()
    else:
        processor.logger.error(f"未知模式: {args.mode}")
        success = False
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
